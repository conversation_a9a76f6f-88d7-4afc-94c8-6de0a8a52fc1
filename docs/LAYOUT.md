# Circuit Layout System

本文档描述了mcLayout库中的自动布局系统，该系统实现了基于力导向算法的电路原理图自动布局功能。

## 概述

布局系统采用自底向上的递归算法，能够：

1. **基础元器件布局** - 根据components目录中的定义自动生成电阻、电容等基础元器件的布局
2. **芯片布局** - 根据引脚数量自动生成矩形芯片布局，按照KiCad习惯设置尺寸和引脚间距
3. **模块布局** - 使用Force-Directed算法对模块内的子组件进行最优布局
4. **递归布局** - 自底向上递归处理整个电路层次结构

## 核心算法

### Force-Directed布局算法

布局系统使用力导向算法来优化组件位置：

- **斥力**: 组件之间存在斥力，防止重叠，距离越近斥力越大
- **拉力**: 通过网络连接的组件之间存在拉力，距离越远拉力越大
- **收敛**: 通过迭代计算直到系统收敛到稳定状态

### 布局流程

1. **初始化**: 将所有组件放置在圆周上
2. **力计算**: 计算每个组件受到的斥力和拉力
3. **位置更新**: 根据受力情况移动组件
4. **迭代优化**: 重复计算直到收敛

## 使用方法

### 基本用法

```rust
use mclayout::{CircuitParser, LayoutEngine};

// 解析电路并生成布局
let (circuit, layouts) = CircuitParser::parse_and_layout_from_file("circuit.json")?;

// 访问布局结果
for layout in &layouts {
    println!("Module: {} at ({:.2}, {:.2})", 
             layout.module_id, layout.position.x, layout.position.y);
    
    for component in layout.get_all_components() {
        println!("  Component: {} at ({:.2}, {:.2})", 
                 component.component_id, component.position.x, component.position.y);
    }
}
```

### 自定义配置

```rust
use mclayout::{LayoutEngine, LayoutConfig, Size};

// 创建自定义配置
let mut config = LayoutConfig::default();
config.max_iterations = 500;           // 最大迭代次数
config.repulsion_strength = 150.0;     // 斥力强度
config.attraction_strength = 0.2;      // 拉力强度
config.min_spacing = 10.0;             // 最小组件间距
config.chip_pin_spacing = 2.54;        // 芯片引脚间距
config.chip_min_size = Size::new(10.0, 10.0); // 芯片最小尺寸

// 使用自定义配置创建布局引擎
let layout_engine = LayoutEngine::with_config(config)?;
let layouts = layout_engine.layout_circuit(&circuit)?;
```

### 单独使用各个布局引擎

```rust
use mclayout::{ComponentLayoutEngine, ChipLayoutEngine, ForceDirectedLayoutEngine};

// 基础元器件布局
let component_engine = ComponentLayoutEngine::new()?;
let component_layout = component_engine.layout_component(&component)?;

// 芯片布局
let chip_engine = ChipLayoutEngine::default();
let chip_layout = chip_engine.layout_chip(&component, 14); // 14引脚芯片

// 力导向布局
let force_engine = ForceDirectedLayoutEngine::default();
force_engine.layout_items(&mut layout_items, &nets)?;
```

## 配置参数

### LayoutConfig

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `max_iterations` | usize | 1000 | 力导向算法的最大迭代次数 |
| `convergence_threshold` | f64 | 0.1 | 收敛阈值，当最大位移小于此值时停止迭代 |
| `repulsion_strength` | f64 | 100.0 | 斥力系数，控制组件间的排斥强度 |
| `attraction_strength` | f64 | 0.1 | 拉力系数，控制连接组件间的吸引强度 |
| `min_spacing` | f64 | 5.0 | 组件间的最小间距 |
| `chip_pin_spacing` | f64 | 2.54 | 芯片引脚间距（标准0.1英寸） |
| `chip_pin_length` | f64 | 2.54 | 芯片引脚长度 |
| `chip_min_size` | Size | 5.08×5.08 | 芯片的最小尺寸 |

## 数据结构

### 布局结果

- **ComponentLayout**: 单个组件的布局结果，包含位置、旋转、尺寸、引脚等信息
- **ModuleLayout**: 模块的布局结果，包含所有子组件的布局
- **LayoutItem**: 布局项，可以是组件或子模块

### 几何类型

- **Point**: 2D坐标点，支持基本向量运算
- **Size**: 尺寸定义（宽度×高度）
- **Rotation**: 旋转角度（以度为单位）
- **Force**: 力向量，用于力导向算法

## 组件定义

系统从`components/`目录加载基础元器件的定义文件（JSON格式），包含：

- 绘图元素（矩形、多边形等）
- 引脚定义（位置、方向、长度）
- 属性信息

示例组件定义：
```json
{
  "name": "R",
  "reference": "R",
  "elements": [
    {
      "name": "rectangle",
      "attrs": {
        "x": -1.016,
        "y": -2.54,
        "width": 2.032,
        "height": 5.08,
        "stroke": "#8B0000",
        "stroke-width": "0.254",
        "fill": "none"
      }
    }
  ],
  "pins": [
    {
      "number": "1",
      "name": "~",
      "type": "passive",
      "position": {"x": 0, "y": 3.81},
      "orientation": "up",
      "length": 1.27
    }
  ]
}
```

## 芯片布局规则

对于芯片类型的组件，系统会：

1. 根据引脚数量计算合适的矩形尺寸
2. 将引脚均匀分布在四边
3. 添加引脚1标记（左上角圆点）
4. 按照KiCad标准设置引脚间距和长度

## 性能优化

- 使用阻尼系数防止振荡
- 限制单步最大移动距离
- 当系统收敛时提前停止迭代
- 支持自定义收敛阈值

## 扩展性

布局系统设计为模块化，可以轻松扩展：

- 添加新的组件类型支持
- 实现自定义布局算法
- 扩展力导向算法的力模型
- 支持更多的几何约束

## 测试

运行布局相关测试：
```bash
cargo test layout
```

运行布局示例：
```bash
cargo run --example layout_example
```
