# Modular Parser System

本文档描述了mcLayout库中的模块化解析系统，该系统提供了灵活、可扩展的电路描述文件解析功能。

## 概述

解析系统采用模块化设计，分为以下几个核心模块：

1. **Core Parser** (`parser::core`) - 核心解析功能和主要API
2. **Validation** (`parser::validation`) - 全面的验证和错误检查
3. **Utilities** (`parser::utils`) - 解析工具和数据提取功能

## 模块结构

```
src/parser/
├── mod.rs          # 模块导出和重新导出
├── core.rs         # 核心解析器实现
├── validation.rs   # 验证引擎
├── utils.rs        # 工具函数和统计
└── tests.rs        # 模块化测试
```

## 核心解析器 (Core Parser)

### CircuitParser

主要的解析器类，提供标准的解析功能：

```rust
use mclayout::CircuitParser;

// 基本解析
let circuit = CircuitParser::parse_from_file("circuit.json")?;
let circuit = CircuitParser::parse_from_str(json_string)?;
let circuit = CircuitParser::parse_from_reader(reader)?;

// 解析并生成布局
let (circuit, layouts) = CircuitParser::parse_and_layout_from_file("circuit.json")?;
let (circuit, layouts) = CircuitParser::parse_and_layout_from_str(json_string)?;

// 综合验证
CircuitParser::validate_comprehensive(&circuit)?;
```

### JsonParser

专门的JSON解析器，提供低级别的JSON处理：

```rust
use mclayout::JsonParser;

// 验证JSON语法
JsonParser::validate_json_syntax(json_string)?;

// 解析JSON（不进行电路验证）
let circuit = JsonParser::parse_str(json_string)?;
let circuit = JsonParser::parse_bytes(json_bytes)?;
let circuit = JsonParser::parse_reader(reader)?;
```

### CircuitParserBuilder

构建器模式，支持灵活的解析配置：

```rust
use mclayout::CircuitParserBuilder;

let circuit = CircuitParserBuilder::new()
    .validate_on_parse(true)        // 解析时验证
    .strict_mode(false)             // 非严格模式
    .allow_empty_modules(true)      // 允许空模块
    .parse_from_file("circuit.json")?;
```

#### 配置选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `validate_on_parse` | bool | true | 解析时是否进行验证 |
| `strict_mode` | bool | false | 是否启用严格模式验证 |
| `allow_empty_modules` | bool | false | 是否允许空模块 |

## 验证系统 (Validation)

### CircuitValidator

专门的验证引擎，提供多层次的验证功能：

```rust
use mclayout::parser::validation::CircuitValidator;

// 基本验证
CircuitValidator::validate_circuit(&circuit)?;

// 带选项的验证
CircuitValidator::validate_circuit_with_options(&circuit, allow_empty_modules)?;

// 连接验证
CircuitValidator::validate_connections(&circuit)?;

// 参数验证
CircuitValidator::validate_parameters(&circuit)?;

// 综合验证
CircuitValidator::validate_comprehensive(&circuit)?;
```

### 验证层次

1. **结构验证** - 检查必需字段、数据类型等
2. **语义验证** - 检查引用完整性、连接有效性等
3. **参数验证** - 检查参数格式、值有效性等
4. **综合验证** - 执行所有验证检查

### 验证规则

- **模块验证**：
  - 必须有class和instance字段
  - 实例名不能包含空格
  - 空模块检查（可配置）

- **组件验证**：
  - 必须有class和instance字段
  - 实例名格式检查
  - 参数值验证

- **连接验证**：
  - 引脚引用有效性
  - 网络连接完整性
  - 总线信号验证

- **参数验证**：
  - 参数名格式检查
  - 参数值非空验证
  - 常见错误检测

## 工具系统 (Utilities)

### ParserUtils

提供丰富的数据提取和分析功能：

```rust
use mclayout::ParserUtils;

// 提取所有组件
let components = ParserUtils::extract_all_components(&circuit);

// 提取所有模块
let modules = ParserUtils::extract_all_modules(&circuit);

// 查找功能
let main_module = ParserUtils::get_main_module(&circuit);
let found_module = ParserUtils::find_module_by_name(&circuit, "power");
let comp_def = ParserUtils::find_component_definition(&circuit, "R");

// 按类型查找组件
let resistors = ParserUtils::find_components_by_class(&circuit, "R");
let components_with_value = ParserUtils::find_components_with_parameter(&circuit, "value");

// 统计信息
let component_count = ParserUtils::count_components(&circuit);
let module_count = ParserUtils::count_modules(&circuit);
let usage_stats = ParserUtils::create_component_usage_stats(&circuit);

// 提取名称列表
let net_names = ParserUtils::extract_net_names(&circuit);
let bus_names = ParserUtils::extract_bus_names(&circuit);
let param_names = ParserUtils::extract_parameter_names(&circuit);

// 层次结构检查
let has_hierarchy = ParserUtils::has_hierarchical_modules(&circuit);
```

### CircuitStatistics

详细的电路统计信息：

```rust
use mclayout::ParserUtils;

let stats = ParserUtils::get_circuit_statistics(&circuit);

// 打印统计摘要
stats.print_summary();

// 访问具体统计数据
println!("Total modules: {}", stats.total_modules);
println!("Total components: {}", stats.total_components);
println!("Has hierarchy: {}", stats.has_hierarchy);

// 组件使用统计
for (component_type, count) in &stats.component_usage_stats {
    println!("{}: {}", component_type, count);
}
```

#### 统计字段

- `total_modules` - 总模块数
- `total_components` - 总组件数
- `total_component_definitions` - 组件定义数
- `total_nets` - 网络数
- `total_buses` - 总线数
- `unique_component_types` - 唯一组件类型数
- `unique_parameter_names` - 唯一参数名数
- `has_hierarchy` - 是否有层次结构
- `component_usage_stats` - 组件使用统计

## 使用示例

### 基本解析流程

```rust
use mclayout::{CircuitParser, ParserUtils};

// 1. 解析电路
let circuit = CircuitParser::parse_from_file("circuit.json")?;

// 2. 获取统计信息
let stats = ParserUtils::get_circuit_statistics(&circuit);
stats.print_summary();

// 3. 查找特定组件
let resistors = ParserUtils::find_components_by_class(&circuit, "R");
println!("Found {} resistors", resistors.len());

// 4. 验证电路
CircuitParser::validate_comprehensive(&circuit)?;
```

### 灵活解析配置

```rust
use mclayout::CircuitParserBuilder;

// 解析可能包含空模块的电路
let circuit = CircuitParserBuilder::new()
    .allow_empty_modules(true)
    .strict_mode(false)
    .parse_from_file("circuit.json")?;

// 仅解析，不验证
let circuit = CircuitParserBuilder::new()
    .validate_on_parse(false)
    .parse_from_file("circuit.json")?;
```

### 错误处理

```rust
use mclayout::{CircuitParser, CircuitParseError};

match CircuitParser::parse_from_file("circuit.json") {
    Ok(circuit) => {
        println!("Successfully parsed circuit");
    }
    Err(CircuitParseError::JsonError(e)) => {
        eprintln!("JSON parsing error: {}", e);
    }
    Err(CircuitParseError::ValidationError(msg)) => {
        eprintln!("Validation error: {}", msg);
    }
    Err(CircuitParseError::MissingField(field)) => {
        eprintln!("Missing required field: {}", field);
    }
    Err(e) => {
        eprintln!("Other error: {}", e);
    }
}
```

## 扩展性

模块化设计使得系统易于扩展：

### 添加新的验证规则

在`validation.rs`中添加新的验证方法：

```rust
impl CircuitValidator {
    pub fn validate_custom_rule(circuit: &CircuitDescription) -> Result<()> {
        // 自定义验证逻辑
        Ok(())
    }
}
```

### 添加新的工具函数

在`utils.rs`中添加新的分析功能：

```rust
impl ParserUtils {
    pub fn extract_custom_info(circuit: &CircuitDescription) -> Vec<String> {
        // 自定义信息提取
        Vec::new()
    }
}
```

### 添加新的解析器

创建专门的解析器处理特定格式：

```rust
pub struct CustomFormatParser;

impl CustomFormatParser {
    pub fn parse_custom_format(data: &str) -> Result<CircuitDescription> {
        // 自定义格式解析
        todo!()
    }
}
```

## 性能考虑

- **惰性验证** - 可选择性地启用/禁用验证
- **增量解析** - 支持流式解析大文件
- **缓存机制** - 统计信息计算结果可缓存
- **内存优化** - 避免不必要的数据复制

## 测试

运行parser相关测试：

```bash
# 运行所有parser测试
cargo test parser

# 运行特定模块测试
cargo test parser::core
cargo test parser::validation
cargo test parser::utils

# 运行示例
cargo run --example parser_modules_example
```
