//! Example demonstrating the modular structure of the circuit parser
//!
//! This example shows how to use different modules of the parser library.

use mclayout::{
    // Main parser
    CircuitParser,
    // Types
    CircuitDescription, Module, Component,
    // Error handling
    Result, CircuitParseError,
};

// You can also import specific modules if needed
use mclayout::parser::CircuitParser as Parser;
use mclayout::types::*;
use mclayout::error::*;

fn main() -> Result<()> {
    println!("=== Circuit Parser Module Structure Demo ===\n");

    // Create a minimal circuit using the main API
    let minimal_json = r#"
    {
        "modules": [
            {
                "type": "module",
                "class": "demo",
                "instance": "demo_circuit",
                "objects": [
                    {
                        "type": "component",
                        "class": "LED",
                        "instance": "led1",
                        "params": {
                            "color": "red",
                            "voltage": "3.3V"
                        }
                    }
                ]
            }
        ],
        "components": [
            {
                "name": "LED",
                "pins": [
                    {"id": 1, "name": ["anode"]},
                    {"id": 2, "name": ["cathode"]}
                ]
            }
        ]
    }
    "#;

    // Parse using the main API
    println!("1. Parsing with main API:");
    let circuit = CircuitParser::parse_from_str(minimal_json)?;
    println!("   ✓ Parsed {} modules", circuit.modules.len());
    println!("   ✓ Found {} component definitions", circuit.components.len());

    // Parse using the explicit parser module
    println!("\n2. Parsing with explicit parser module:");
    let circuit2 = Parser::parse_from_str(minimal_json)?;
    println!("   ✓ Same result: {} modules", circuit2.modules.len());

    // Demonstrate error handling
    println!("\n3. Error handling demonstration:");
    let invalid_json = r#"{"invalid": "json structure"}"#;
    match CircuitParser::parse_from_str(invalid_json) {
        Ok(_) => println!("   Unexpected success"),
        Err(CircuitParseError::JsonError(e)) => {
            println!("   ✓ Caught JSON error: {}", e);
        }
        Err(CircuitParseError::ValidationError(msg)) => {
            println!("   ✓ Caught validation error: {}", msg);
        }
        Err(e) => {
            println!("   ✓ Caught other error: {}", e);
        }
    }

    // Demonstrate type usage
    println!("\n4. Working with types:");
    if let Some(main_module) = circuit.main_module() {
        println!("   ✓ Main module: {}", main_module.instance);
        
        for component in main_module.components() {
            println!("   ✓ Component: {} ({})", component.instance, component.class);
            
            // Access component parameters
            if let Some(color) = component.get_param_str("color") {
                println!("     - Color: {}", color);
            }
            if let Some(voltage) = component.get_param_str("voltage") {
                println!("     - Voltage: {}", voltage);
            }
        }
    }

    // Demonstrate validation
    println!("\n5. Validation:");
    match CircuitParser::validate_comprehensive(&circuit) {
        Ok(()) => println!("   ✓ Circuit validation passed"),
        Err(e) => println!("   ✗ Validation failed: {}", e),
    }

    println!("\n=== Module structure demo completed successfully ===");
    Ok(())
}
