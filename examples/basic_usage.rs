//! Basic usage example for the circuit description parser
//!
//! This example demonstrates how to parse a circuit description file
//! and access various parts of the circuit data.

use mclayout::{CircuitParser, Result};

fn main() -> Result<()> {
    // Create a simple test circuit since the reference file contains JSON comments
    let test_json = r#"
    {
        "modules": [
            {
                "type": "module",
                "class": "main",
                "instance": "main",
                "objects": [
                    {
                        "type": "component",
                        "class": "RES",
                        "instance": "R1",
                        "params": {
                            "rs": "10kΩ",
                            "pkg": "R0603"
                        }
                    },
                    {
                        "type": "module",
                        "class": "POWER",
                        "instance": "power",
                        "objects": [
                            {
                                "type": "component",
                                "class": "CAP",
                                "instance": "C1",
                                "params": {
                                    "cap": "100nF",
                                    "volt": "25V"
                                }
                            }
                        ],
                        "pins": [
                            {"name": "VIN", "type": "input"},
                            {"name": "VOUT", "type": "output"}
                        ],
                        "nets": [
                            {
                                "name": "VIN_NET",
                                "connection": ["VIN", "C1.1"]
                            }
                        ],
                        "buses": []
                    }
                ],
                "pins": ["VDD", "GND"],
                "nets": [
                    {
                        "name": "VDD_NET",
                        "connection": ["R1.1", "power.VIN"]
                    }
                ],
                "buses": []
            }
        ],
        "components": [
            {
                "name": "RES",
                "class": "",
                "partno": "",
                "package": "",
                "pins": [
                    {"id": 1},
                    {"id": 2}
                ]
            },
            {
                "name": "CAP",
                "class": "",
                "partno": "",
                "package": "",
                "pins": [
                    {"id": 1},
                    {"id": 2}
                ]
            }
        ]
    }
    "#;

    // Parse the test circuit
    let circuit = CircuitParser::parse_from_str(test_json)?;
    
    println!("=== Circuit Description Parser Example ===\n");
    
    // Display basic circuit information
    println!("Total modules: {}", circuit.modules.len());
    println!("Total component definitions: {}", circuit.components.len());
    
    // Access the main module
    if let Some(main_module) = circuit.main_module() {
        println!("\n=== Main Module ===");
        println!("Class: {}", main_module.class);
        println!("Instance: {}", main_module.instance);
        println!("Objects: {}", main_module.objects.len());
        println!("Nets: {}", main_module.nets.len());
        println!("Buses: {}", main_module.buses.len());
        
        // List all nested modules
        println!("\n=== Nested Modules ===");
        for nested_module in main_module.nested_modules() {
            println!("- {} ({})", nested_module.instance, nested_module.class);
        }
    }
    
    // Find specific modules
    println!("\n=== Module Search Examples ===");

    if let Some(power_module) = circuit.find_module("power") {
        println!("Found power module:");
        println!("  Class: {}", power_module.class);
        println!("  Components: {}", power_module.components().len());

        // List components in this module
        for component in power_module.components() {
            println!("    - {} ({})", component.instance, component.class);

            // Show component parameters
            if !component.params.is_empty() {
                println!("      Parameters:");
                for (key, value) in &component.params {
                    println!("        {}: {}", key, value);
                }
            }
        }
    }
    
    // Component definition examples
    println!("\n=== Component Definitions ===");
    
    if let Some(resistor_def) = circuit.find_component_definition("RES") {
        println!("Resistor definition found:");
        if !resistor_def.class.is_empty() {
            println!("  Class: {}", resistor_def.class);
        }
        println!("  Pins: {}", resistor_def.pins.len());
    }
    
    // Show all component instances across the circuit
    println!("\n=== All Components ===");
    let all_components = circuit.all_components();
    println!("Total component instances: {}", all_components.len());
    
    // Group by component class
    let mut component_counts = HashMap::new();
    for (_, component) in &all_components {
        *component_counts.entry(&component.class).or_insert(0) += 1;
    }
    
    println!("Component types:");
    for (class, count) in component_counts {
        println!("  {}: {} instances", class, count);
    }
    
    // Validation example
    println!("\n=== Validation ===");
    match CircuitParser::validate_comprehensive(&circuit) {
        Ok(()) => println!("✓ Circuit validation passed"),
        Err(e) => println!("✗ Validation error: {}", e),
    }
    
    println!("\n=== Example completed successfully ===");
    Ok(())
}

use std::collections::HashMap;
