use mclayout::{
    CircuitParser, CircuitParserBuilder, JsonParser, ParserUtils, CircuitStatistics,
    CircuitDescription, Module, Component, CircuitObject, ModulePins, Net,
};
use std::collections::HashMap;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Modular Parser Example");
    println!("=====================");

    // Example 1: Basic JSON parsing with JsonParser
    println!("\n1. Basic JSON Parsing");
    let json_str = r#"{"modules": [], "components": []}"#;
    
    // Validate JSON syntax first
    JsonParser::validate_json_syntax(json_str)?;
    println!("✓ JSON syntax is valid");
    
    // Parse the JSON
    let circuit = JsonParser::parse_str(json_str)?;
    println!("✓ Parsed circuit with {} modules", circuit.modules.len());

    // Example 2: Using CircuitParserBuilder for flexible parsing
    println!("\n2. Flexible Parsing with Builder Pattern");
    
    let flexible_json = r#"{
        "modules": [
            {
                "type": "module",
                "class": "EmptyModule", 
                "instance": "empty",
                "objects": [],
                "pins": [],
                "nets": [],
                "buses": []
            }
        ],
        "components": []
    }"#;
    
    // Parse with strict validation disabled and empty modules allowed
    let circuit = CircuitParserBuilder::new()
        .validate_on_parse(true)
        .allow_empty_modules(true)
        .strict_mode(false)
        .parse_from_str(flexible_json)?;
    
    println!("✓ Parsed circuit with flexible settings");
    println!("  Modules: {}", circuit.modules.len());

    // Example 3: Create a test circuit and use ParserUtils
    println!("\n3. Circuit Analysis with ParserUtils");
    
    let test_circuit = create_test_circuit();
    
    // Extract various information using ParserUtils
    let all_components = ParserUtils::extract_all_components(&test_circuit);
    let all_modules = ParserUtils::extract_all_modules(&test_circuit);
    let net_names = ParserUtils::extract_net_names(&test_circuit);
    let component_usage = ParserUtils::create_component_usage_stats(&test_circuit);
    
    println!("Circuit Analysis:");
    println!("  Total components: {}", all_components.len());
    println!("  Total modules: {}", all_modules.len());
    println!("  Net names: {:?}", net_names);
    println!("  Component usage: {:?}", component_usage);
    
    // Find specific components
    let resistors = ParserUtils::find_components_by_class(&test_circuit, "R");
    println!("  Resistors found: {}", resistors.len());
    
    let components_with_value = ParserUtils::find_components_with_parameter(&test_circuit, "value");
    println!("  Components with 'value' parameter: {}", components_with_value.len());

    // Example 4: Circuit Statistics
    println!("\n4. Detailed Circuit Statistics");
    
    let stats = ParserUtils::get_circuit_statistics(&test_circuit);
    stats.print_summary();

    // Example 5: Module and Component Definition Lookup
    println!("\n5. Lookup Operations");
    
    if let Some(main_module) = ParserUtils::get_main_module(&test_circuit) {
        println!("✓ Found main module: {}", main_module.instance);
    }
    
    if let Some(found_module) = ParserUtils::find_module_by_name(&test_circuit, "main") {
        println!("✓ Found module by name: {}", found_module.instance);
    }
    
    if let Some(comp_def) = ParserUtils::find_component_definition(&test_circuit, "R") {
        println!("✓ Found component definition: {}", comp_def.name);
    }

    // Example 6: Comprehensive validation
    println!("\n6. Validation");
    
    match CircuitParser::validate_comprehensive(&test_circuit) {
        Ok(()) => println!("✓ Circuit passed comprehensive validation"),
        Err(e) => println!("✗ Validation failed: {}", e),
    }

    // Example 7: Parse and layout in one step
    println!("\n7. Parse and Layout Integration");
    
    // Create a simple circuit JSON
    let circuit_json = create_simple_circuit_json();
    
    match CircuitParser::parse_and_layout_from_str(&circuit_json) {
        Ok((circuit, layouts)) => {
            println!("✓ Successfully parsed and generated layout");
            println!("  Circuit modules: {}", circuit.modules.len());
            println!("  Layout modules: {}", layouts.len());
            
            for layout in &layouts {
                println!("  Module '{}' layout at ({:.2}, {:.2})", 
                         layout.module_id, layout.position.x, layout.position.y);
            }
        }
        Err(e) => println!("✗ Parse and layout failed: {}", e),
    }

    Ok(())
}

fn create_test_circuit() -> CircuitDescription {
    CircuitDescription {
        modules: vec![
            Module {
                module_type: "module".to_string(),
                class: "TestCircuit".to_string(),
                instance: "main".to_string(),
                objects: vec![
                    CircuitObject::Component(Component {
                        class: "R".to_string(),
                        instance: "R1".to_string(),
                        params: {
                            let mut params = HashMap::new();
                            params.insert("value".to_string(), serde_json::Value::String("10k".to_string()));
                            params.insert("package".to_string(), serde_json::Value::String("0603".to_string()));
                            params
                        },
                    }),
                    CircuitObject::Component(Component {
                        class: "R".to_string(),
                        instance: "R2".to_string(),
                        params: {
                            let mut params = HashMap::new();
                            params.insert("value".to_string(), serde_json::Value::String("1k".to_string()));
                            params
                        },
                    }),
                    CircuitObject::Component(Component {
                        class: "C".to_string(),
                        instance: "C1".to_string(),
                        params: {
                            let mut params = HashMap::new();
                            params.insert("value".to_string(), serde_json::Value::String("100nF".to_string()));
                            params
                        },
                    }),
                ],
                pins: ModulePins::Simple(vec!["VCC".to_string(), "GND".to_string(), "OUT".to_string()]),
                nets: vec![
                    Net {
                        name: "VCC".to_string(),
                        net_type: Some("power".to_string()),
                        connection: vec!["R1.1".to_string(), "VCC".to_string()],
                        signals: vec![],
                    },
                    Net {
                        name: "GND".to_string(),
                        net_type: Some("power".to_string()),
                        connection: vec!["R2.2".to_string(), "C1.2".to_string(), "GND".to_string()],
                        signals: vec![],
                    },
                    Net {
                        name: "signal".to_string(),
                        net_type: None,
                        connection: vec!["R1.2".to_string(), "R2.1".to_string(), "C1.1".to_string(), "OUT".to_string()],
                        signals: vec![],
                    },
                ],
                buses: vec![],
            }
        ],
        components: vec![
            mclayout::ComponentDefinition {
                name: "R".to_string(),
                class: "Resistor".to_string(),
                partno: "".to_string(),
                package: "".to_string(),
                attr: HashMap::new(),
                pins: vec![
                    mclayout::ComponentPin {
                        id: serde_json::Value::Number(1.into()),
                        name: vec!["~".to_string()],
                        attr: HashMap::new(),
                    },
                    mclayout::ComponentPin {
                        id: serde_json::Value::Number(2.into()),
                        name: vec!["~".to_string()],
                        attr: HashMap::new(),
                    },
                ],
                nets: vec![],
                buses: vec![],
                objects: vec![],
            },
            mclayout::ComponentDefinition {
                name: "C".to_string(),
                class: "Capacitor".to_string(),
                partno: "".to_string(),
                package: "".to_string(),
                attr: HashMap::new(),
                pins: vec![
                    mclayout::ComponentPin {
                        id: serde_json::Value::Number(1.into()),
                        name: vec!["~".to_string()],
                        attr: HashMap::new(),
                    },
                    mclayout::ComponentPin {
                        id: serde_json::Value::Number(2.into()),
                        name: vec!["~".to_string()],
                        attr: HashMap::new(),
                    },
                ],
                nets: vec![],
                buses: vec![],
                objects: vec![],
            },
        ],
    }
}

fn create_simple_circuit_json() -> String {
    r#"{
        "modules": [
            {
                "type": "module",
                "class": "SimpleCircuit",
                "instance": "main",
                "objects": [
                    {
                        "type": "component",
                        "class": "R",
                        "instance": "R1",
                        "params": {"value": "1k"}
                    },
                    {
                        "type": "component", 
                        "class": "C",
                        "instance": "C1",
                        "params": {"value": "100nF"}
                    }
                ],
                "pins": ["VCC", "GND"],
                "nets": [
                    {
                        "name": "VCC",
                        "connection": ["R1.1", "VCC"]
                    },
                    {
                        "name": "GND", 
                        "connection": ["C1.2", "GND"]
                    }
                ],
                "buses": []
            }
        ],
        "components": []
    }"#.to_string()
}
