use mclayout::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>out<PERSON><PERSON><PERSON>, LayoutConfig, <PERSON>ze, Point};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Circuit Layout Example");
    println!("=====================");

    // Example 1: Parse and layout from the test file
    if let Ok((circuit, layouts)) = CircuitParser::parse_and_layout_from_file("refs/hbl.json") {
        println!("\n1. Parsed and laid out circuit from refs/hbl.json");
        
        for layout in &layouts {
            println!("Module: {} at ({:.2}, {:.2})", 
                     layout.module_id, layout.position.x, layout.position.y);
            println!("  Size: {:.2} x {:.2}", layout.size.width, layout.size.height);
            println!("  Pins: {}", layout.pins.len());
            
            // Show component layouts
            for component in layout.get_all_components() {
                println!("    Component: {} at ({:.2}, {:.2})", 
                         component.component_id, component.position.x, component.position.y);
                println!("      Size: {:.2} x {:.2}", component.size.width, component.size.height);
                println!("      Pins: {}", component.pins.len());
            }
        }
    } else {
        println!("Could not parse refs/hbl.json, creating example circuit instead");
        
        // Example 2: Create a simple circuit programmatically
        create_example_circuit()?;
    }

    // Example 3: Custom layout configuration
    println!("\n3. Custom Layout Configuration Example");
    custom_layout_config_example()?;

    Ok(())
}

fn create_example_circuit() -> Result<(), Box<dyn std::error::Error>> {
    use mclayout::*;
    use std::collections::HashMap;

    println!("\n2. Creating example circuit programmatically");

    // Create a simple circuit with resistors and capacitors
    let mut circuit = CircuitDescription {
        modules: vec![
            Module {
                module_type: "module".to_string(),
                class: "SimpleCircuit".to_string(),
                instance: "main".to_string(),
                objects: vec![
                    CircuitObject::Component(Component {
                        class: "R".to_string(),
                        instance: "R1".to_string(),
                        params: HashMap::new(),
                    }),
                    CircuitObject::Component(Component {
                        class: "R".to_string(),
                        instance: "R2".to_string(),
                        params: HashMap::new(),
                    }),
                    CircuitObject::Component(Component {
                        class: "C".to_string(),
                        instance: "C1".to_string(),
                        params: HashMap::new(),
                    }),
                    CircuitObject::Component(Component {
                        class: "IC".to_string(),
                        instance: "U1".to_string(),
                        params: {
                            let mut params = HashMap::new();
                            params.insert("pins".to_string(), serde_json::Value::Number(14.into()));
                            params
                        },
                    }),
                ],
                pins: ModulePins::Simple(vec!["VCC".to_string(), "GND".to_string()]),
                nets: vec![
                    Net {
                        name: "VCC".to_string(),
                        net_type: Some("power".to_string()),
                        connection: vec!["R1.1".to_string(), "U1.14".to_string()],
                        signals: vec![],
                    },
                    Net {
                        name: "GND".to_string(),
                        net_type: Some("power".to_string()),
                        connection: vec!["R2.2".to_string(), "C1.2".to_string(), "U1.7".to_string()],
                        signals: vec![],
                    },
                ],
                buses: vec![],
            }
        ],
        components: vec![],
    };

    // Generate layout
    let layouts = circuit.generate_layout()?;

    println!("Generated layout for example circuit:");
    for layout in &layouts {
        println!("Module: {} at ({:.2}, {:.2})", 
                 layout.module_id, layout.position.x, layout.position.y);
        
        for component in layout.get_all_components() {
            println!("  Component: {} ({}) at ({:.2}, {:.2})", 
                     component.component_id, 
                     component.component_id.chars().take(1).collect::<String>(),
                     component.position.x, component.position.y);
        }
    }

    Ok(())
}

fn custom_layout_config_example() -> Result<(), Box<dyn std::error::Error>> {
    use mclayout::*;

    // Create custom layout configuration
    let mut config = LayoutConfig::default();
    config.max_iterations = 500;
    config.repulsion_strength = 200.0;
    config.attraction_strength = 0.2;
    config.min_spacing = 10.0;
    config.chip_pin_spacing = 2.54;
    config.chip_min_size = Size::new(10.0, 10.0);

    println!("Custom layout configuration:");
    println!("  Max iterations: {}", config.max_iterations);
    println!("  Repulsion strength: {}", config.repulsion_strength);
    println!("  Attraction strength: {}", config.attraction_strength);
    println!("  Min spacing: {}", config.min_spacing);
    println!("  Chip pin spacing: {}", config.chip_pin_spacing);

    // Create layout engine with custom config
    let layout_engine = LayoutEngine::with_config(config)?;
    println!("Layout engine created with custom configuration");

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_layout_example() {
        // This test ensures the example compiles and runs without panicking
        assert!(create_example_circuit().is_ok());
        assert!(custom_layout_config_example().is_ok());
    }
}
