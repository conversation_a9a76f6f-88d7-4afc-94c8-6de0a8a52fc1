// examples/test_hbl_parsing.rs
use mclayout::{
    CircuitParser, CircuitParserBuilder, JsonParser, ParserUtils, 
    CircuitDescription, CircuitParseError
};
use std::time::Instant;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("HBL Circuit Parsing Test");
    println!("========================");

    // 测试1: 基本解析能力
    test_basic_parsing()?;
    
    // 测试2: 灵活解析配置
    test_flexible_parsing()?;
    
    // 测试3: 详细分析和统计
    test_detailed_analysis()?;
    
    // 测试4: 性能测试
    test_performance()?;
    
    // 测试5: 验证测试
    test_validation()?;
    
    // 测试6: 布局集成测试
    test_layout_integration()?;

    println!("\n✅ 所有HBL解析测试完成!");
    Ok(())
}

fn test_basic_parsing() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n1. 基本解析测试");
    println!("================");
    
    match CircuitParser::parse_from_file("refs/hbl.json") {
        Ok(circuit) => {
            println!("✅ 成功解析 hbl.json");
            println!("   模块数量: {}", circuit.modules.len());
            println!("   组件定义数量: {}", circuit.components.len());
            
            // 验证主模块
            if let Some(main_module) = ParserUtils::get_main_module(&circuit) {
                println!("   主模块: {}", main_module.instance);
                println!("   主模块子对象数量: {}", main_module.objects.len());
            }
        }
        Err(e) => {
            println!("❌ 解析失败: {}", e);
            match e {
                CircuitParseError::JsonError(json_err) => {
                    println!("   JSON错误详情: {}", json_err);
                    println!("   提示: hbl.json可能包含JSON注释或格式问题");
                }
                CircuitParseError::ValidationError(msg) => {
                    println!("   验证错误: {}", msg);
                }
                _ => {
                    println!("   其他错误: {:?}", e);
                }
            }
            return Err(e.into());
        }
    }
    
    Ok(())
}

fn test_flexible_parsing() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n2. 灵活解析配置测试");
    println!("====================");
    
    // 尝试不同的解析配置
    let configs = vec![
        ("严格模式", true, false, true),
        ("宽松模式", false, true, false),
        ("仅解析不验证", false, true, false),
    ];
    
    for (name, strict, allow_empty, validate) in configs {
        println!("\n   测试配置: {}", name);
        
        let result = CircuitParserBuilder::new()
            .strict_mode(strict)
            .allow_empty_modules(allow_empty)
            .validate_on_parse(validate)
            .parse_from_file("refs/hbl.json");
            
        match result {
            Ok(circuit) => {
                println!("   ✅ {} 解析成功", name);
                println!("      模块数: {}", circuit.modules.len());
            }
            Err(e) => {
                println!("   ❌ {} 解析失败: {}", name, e);
            }
        }
    }
    
    Ok(())
}

fn test_detailed_analysis() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n3. 详细分析测试");
    println!("================");
    
    // 首先尝试解析，如果失败则跳过分析
    let circuit = match CircuitParser::parse_from_file("refs/hbl.json") {
        Ok(c) => c,
        Err(e) => {
            println!("   ⚠️  跳过分析测试，因为解析失败: {}", e);
            return Ok(());
        }
    };
    
    // 获取详细统计
    let stats = ParserUtils::get_circuit_statistics(&circuit);
    println!("\n   📊 电路统计信息:");
    stats.print_summary();
    
    // 分析模块结构
    println!("\n   🏗️  模块结构分析:");
    let all_modules = ParserUtils::extract_all_modules(&circuit);
    for module in &all_modules {
        println!("      模块: {} (类型: {})", module.instance, module.class);
        println!("         子对象: {}", module.objects.len());
        println!("         网络: {}", module.nets.len());
        println!("         总线: {}", module.buses.len());
    }
    
    // 分析组件使用情况
    println!("\n   🔧 组件使用分析:");
    let all_components = ParserUtils::extract_all_components(&circuit);
    println!("      总组件实例数: {}", all_components.len());
    
    // 按类型分组
    let mut component_types = std::collections::HashMap::new();
    for (_, component) in &all_components {
        *component_types.entry(component.class.clone()).or_insert(0) += 1;
    }
    
    for (comp_type, count) in component_types {
        println!("      {}: {} 个实例", comp_type, count);
    }
    
    // 网络分析
    println!("\n   🔗 网络连接分析:");
    let net_names = ParserUtils::extract_net_names(&circuit);
    println!("      网络数量: {}", net_names.len());
    
    // 显示主要网络
    let important_nets = ["GND", "VCC", "VDD_3V3", "POWER_SYS"];
    for net in &important_nets {
        if net_names.contains(&net.to_string()) {
            println!("      ✅ 发现重要网络: {}", net);
        }
    }
    
    // 总线分析
    let bus_names = ParserUtils::extract_bus_names(&circuit);
    if !bus_names.is_empty() {
        println!("\n   🚌 总线分析:");
        println!("      总线数量: {}", bus_names.len());
        for bus in &bus_names {
            println!("      总线: {}", bus);
        }
    }
    
    Ok(())
}

fn test_performance() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n4. 性能测试");
    println!("============");
    
    let iterations = 5;
    let mut parse_times = Vec::new();
    
    for i in 1..=iterations {
        println!("   第 {} 次解析...", i);
        
        let start = Instant::now();
        let result = CircuitParser::parse_from_file("refs/hbl.json");
        let duration = start.elapsed();
        
        match result {
            Ok(_) => {
                parse_times.push(duration);
                println!("      ✅ 解析时间: {:?}", duration);
            }
            Err(e) => {
                println!("      ❌ 解析失败: {}", e);
                break;
            }
        }
    }
    
    if !parse_times.is_empty() {
        let avg_time = parse_times.iter().sum::<std::time::Duration>() / parse_times.len() as u32;
        let min_time = parse_times.iter().min().unwrap();
        let max_time = parse_times.iter().max().unwrap();
        
        println!("\n   📈 性能统计:");
        println!("      平均解析时间: {:?}", avg_time);
        println!("      最快解析时间: {:?}", min_time);
        println!("      最慢解析时间: {:?}", max_time);
        
        if avg_time.as_millis() > 1000 {
            println!("      ⚠️  解析时间较长，可能需要优化");
        } else {
            println!("      ✅ 解析性能良好");
        }
    }
    
    Ok(())
}

fn test_validation() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n5. 验证测试");
    println!("============");
    
    let circuit = match CircuitParser::parse_from_file("refs/hbl.json") {
        Ok(c) => c,
        Err(e) => {
            println!("   ⚠️  跳过验证测试，因为解析失败: {}", e);
            return Ok(());
        }
    };
    
    // 综合验证
    println!("   🔍 执行综合验证...");
    match CircuitParser::validate_comprehensive(&circuit) {
        Ok(()) => {
            println!("   ✅ 综合验证通过");
        }
        Err(e) => {
            println!("   ❌ 验证失败: {}", e);
            println!("      这可能表明电路描述存在问题");
        }
    }
    
    // 检查常见问题
    println!("\n   🔍 检查常见问题:");
    
    // 检查是否有空模块
    let all_modules = ParserUtils::extract_all_modules(&circuit);
    let empty_modules: Vec<_> = all_modules.iter()
        .filter(|m| m.objects.is_empty() && m.nets.is_empty() && m.buses.is_empty())
        .collect();
    
    if empty_modules.is_empty() {
        println!("      ✅ 没有发现空模块");
    } else {
        println!("      ⚠️  发现 {} 个空模块", empty_modules.len());
        for module in empty_modules {
            println!("         空模块: {}", module.instance);
        }
    }
    
    // 检查组件引用
    let all_components = ParserUtils::extract_all_components(&circuit);
    let mut undefined_components = Vec::new();
    
    for (_, component) in &all_components {
        if ParserUtils::find_component_definition(&circuit, &component.class).is_none() {
            undefined_components.push(&component.class);
        }
    }
    
    if undefined_components.is_empty() {
        println!("      ✅ 所有组件都有定义");
    } else {
        println!("      ⚠️  发现 {} 个未定义的组件类型", undefined_components.len());
        let mut unique_undefined: Vec<_> = undefined_components.into_iter().collect();
        unique_undefined.sort();
        unique_undefined.dedup();
        for comp_type in unique_undefined {
            println!("         未定义组件: {}", comp_type);
        }
    }
    
    Ok(())
}

fn test_layout_integration() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n6. 布局集成测试");
    println!("================");
    
    println!("   🎨 尝试解析并生成布局...");
    
    match CircuitParser::parse_and_layout_from_file("refs/hbl.json") {
        Ok((circuit, layouts)) => {
            println!("   ✅ 解析和布局生成成功!");
            println!("      电路模块数: {}", circuit.modules.len());
            println!("      布局模块数: {}", layouts.len());
            
            for layout in &layouts {
                println!("      模块 '{}' 布局位置: ({:.2}, {:.2})", 
                         layout.module_id, layout.position.x, layout.position.y);
                println!("         尺寸: {:.2} x {:.2}", 
                         layout.size.width, layout.size.height);
                
                let component_count = layout.get_all_components().len();
                println!("         包含组件: {} 个", component_count);
            }
        }
        Err(e) => {
            println!("   ❌ 解析或布局生成失败: {}", e);
            println!("      这可能是由于:");
            println!("      - JSON格式问题");
            println!("      - 电路结构复杂度");
            println!("      - 组件定义不完整");
        }
    }
    
    Ok(())
}

// 辅助函数：创建简化版本的HBL电路用于测试
fn create_simplified_hbl_circuit() -> String {
    r#"{
        "modules": [
            {
                "type": "module",
                "class": "main",
                "instance": "main",
                "objects": [
                    {
                        "type": "module",
                        "class": "POWER_USB",
                        "instance": "pwusb",
                        "objects": [
                            {
                                "type": "component",
                                "class": "USB.MINI_SOCKET",
                                "instance": "usb1"
                            },
                            {
                                "type": "component",
                                "class": "RES",
                                "instance": "RES0",
                                "params": {
                                    "rs": "0R",
                                    "pkg": "R0603"
                                }
                            }
                        ],
                        "pins": [
                            {"name": "POWER_SYS", "type": "inout"},
                            {"name": "GND", "type": "inout"}
                        ],
                        "nets": [
                            {
                                "name": "GND",
                                "connection": ["usb1.5", "GND"]
                            }
                        ]
                    }
                ],
                "nets": [
                    {
                        "name": "GND",
                        "connection": ["pwusb.GND"]
                    }
                ]
            }
        ],
        "components": [
            {
                "name": "USB.MINI_SOCKET",
                "pins": [
                    {"id": 1, "name": ["VBUS"]},
                    {"id": 5, "name": ["GND"]}
                ]
            },
            {
                "class": "mc.res.RES",
                "pins": [
                    {"id": 1},
                    {"id": 2}
                ]
            }
        ]
    }"#.to_string()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_hbl_parsing_unit() {
        // 单元测试版本，用于自动化测试
        let result = CircuitParser::parse_from_file("refs/hbl.json");
        
        match result {
            Ok(circuit) => {
                assert!(!circuit.modules.is_empty(), "应该包含至少一个模块");
                assert!(!circuit.components.is_empty(), "应该包含组件定义");
                
                // 验证主模块存在
                let main_module = ParserUtils::get_main_module(&circuit);
                assert!(main_module.is_some(), "应该有主模块");
            }
            Err(CircuitParseError::JsonError(_)) => {
                // JSON错误是预期的，因为文件可能包含注释
                println!("预期的JSON错误 - hbl.json包含注释");
            }
            Err(e) => {
                panic!("意外的解析错误: {}", e);
            }
        }
    }

    #[test]
    fn test_simplified_hbl_parsing() {
        // 测试简化版本
        let simplified_json = create_simplified_hbl_circuit();
        let result = CircuitParser::parse_from_str(&simplified_json);
        
        assert!(result.is_ok(), "简化版HBL电路应该能够解析");
        
        let circuit = result.unwrap();
        assert_eq!(circuit.modules.len(), 1);
        
        let stats = ParserUtils::get_circuit_statistics(&circuit);
        assert!(stats.has_hierarchy, "应该有层次结构");
    }
}

