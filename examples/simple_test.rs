//! Simple test to verify the parser works with minimal JSON

use mclayout::{CircuitParser, Result};

fn main() -> Result<()> {
    // Create a minimal valid circuit
    let minimal_json = r#"
    {
        "modules": [
            {
                "type": "module",
                "class": "main",
                "instance": "main"
            }
        ]
    }
    "#;
    
    println!("Testing minimal circuit parsing...");
    
    // Parse the minimal circuit
    match CircuitParser::parse_from_str(minimal_json) {
        Ok(circuit) => {
            println!("✓ Successfully parsed minimal circuit!");
            println!("  Modules: {}", circuit.modules.len());
            println!("  Components: {}", circuit.components.len());
            
            if let Some(main_module) = circuit.main_module() {
                println!("  Main module found: {}", main_module.instance);
            }
        }
        Err(e) => {
            println!("✗ Failed to parse: {}", e);
            return Err(e);
        }
    }
    
    // Test with a slightly more complex circuit
    let simple_json = r#"
    {
        "modules": [
            {
                "type": "module",
                "class": "main",
                "instance": "main",
                "objects": [
                    {
                        "type": "component",
                        "class": "RES",
                        "instance": "R1"
                    }
                ]
            }
        ],
        "components": [
            {
                "name": "RES"
            }
        ]
    }
    "#;
    
    println!("\nTesting simple circuit with component...");
    
    match CircuitParser::parse_from_str(simple_json) {
        Ok(circuit) => {
            println!("✓ Successfully parsed simple circuit!");
            println!("  Modules: {}", circuit.modules.len());
            println!("  Components: {}", circuit.components.len());
            
            let all_components = circuit.all_components();
            println!("  Total component instances: {}", all_components.len());
            
            for (module, component) in all_components {
                println!("    {} in module {}", component.instance, module.instance);
            }
        }
        Err(e) => {
            println!("✗ Failed to parse: {}", e);
            return Err(e);
        }
    }
    
    println!("\n✓ All tests passed! The circuit parser is working correctly.");
    Ok(())
}
