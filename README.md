# Circuit Description Parser & Layout Engine

A Rust library for parsing circuit description files in JSON format and automatically generating circuit layouts. This library can handle hierarchical circuit designs with modules, components, connections, and parameters, and provides automatic layout generation using force-directed algorithms.

## Features

### Circuit Parsing
- **Hierarchical Design Support**: Parse nested modules and sub-circuits
- **Component Management**: Handle component instances with parameters
- **Connection Validation**: Validate nets, pins, and bus connections
- **Comprehensive Error Handling**: Detailed error messages for debugging
- **Convenient API**: Easy-to-use methods for accessing circuit data
- **Parameter Handling**: Support for component specifications and attributes

### Automatic Layout Generation
- **Force-Directed Layout**: Automatic component placement using physics-based algorithms
- **Component Library**: Support for basic components (resistors, capacitors, etc.) from KiCad
- **Chip Layout**: Automatic IC layout with configurable pin arrangements
- **Hierarchical Layout**: Bottom-up recursive layout for complex circuits
- **Customizable Parameters**: Configurable spacing, forces, and layout constraints

## Installation

Add this to your `Cargo.toml`:

```toml
[dependencies]
mclayout = "0.1.0"
```

## Quick Start

### Basic Parsing

```rust
use mclayout::{CircuitParser, Result};

fn main() -> Result<()> {
    // Parse from file
    let circuit = CircuitParser::parse_from_file("circuit.json")?;

    // Access the main module
    if let Some(main_module) = circuit.main_module() {
        println!("Main module: {}", main_module.instance);

        // Find components
        for component in main_module.components() {
            println!("Component: {} ({})", component.instance, component.class);
        }
    }

    Ok(())
}
```

### Parsing with Automatic Layout

```rust
use mclayout::{CircuitParser, LayoutEngine};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Parse and generate layout in one step
    let (circuit, layouts) = CircuitParser::parse_and_layout_from_file("circuit.json")?;

    // Access layout results
    for layout in &layouts {
        println!("Module: {} at ({:.2}, {:.2})",
                 layout.module_id, layout.position.x, layout.position.y);

        // Get all components in this module
        for component in layout.get_all_components() {
            println!("  Component: {} at ({:.2}, {:.2})",
                     component.component_id, component.position.x, component.position.y);
        }
    }

    Ok(())
}
```

### Custom Layout Configuration

```rust
use mclayout::{LayoutEngine, LayoutConfig, Size};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let circuit = CircuitParser::parse_from_file("circuit.json")?;

    // Create custom layout configuration
    let mut config = LayoutConfig::default();
    config.max_iterations = 500;
    config.repulsion_strength = 150.0;
    config.min_spacing = 10.0;

    // Generate layout with custom config
    let layouts = circuit.generate_layout_with_config(config)?;

    Ok(())
}
```

## Circuit Description Format

The parser expects JSON files with the following structure:

```json
{
    "modules": [
        {
            "type": "module",
            "class": "main",
            "instance": "main",
            "objects": [
                {
                    "type": "component",
                    "class": "RES",
                    "instance": "R1",
                    "params": {
                        "rs": "10kΩ",
                        "pkg": "R0603"
                    }
                }
            ],
            "pins": [
                {"name": "VDD", "type": "input"},
                {"name": "GND", "type": "inout"}
            ],
            "nets": [
                {
                    "name": "VDD_NET",
                    "connection": ["R1.1", "VDD"]
                }
            ]
        }
    ],
    "components": [
        {
            "name": "RES",
            "pins": [
                {"id": 1},
                {"id": 2}
            ]
        }
    ]
}
```

## API Reference

### Main Parser

- `CircuitParser::parse_from_file(path)` - Parse from file
- `CircuitParser::parse_from_str(json)` - Parse from JSON string
- `CircuitParser::parse_from_reader(reader)` - Parse from reader
- `CircuitParser::validate_comprehensive(circuit)` - Full validation
- `CircuitParser::parse_and_layout_from_file(path)` - Parse and generate layout
- `CircuitParser::parse_and_layout_from_str(json)` - Parse and generate layout from string

### Layout Engine

- `LayoutEngine::new()` - Create layout engine with default config
- `LayoutEngine::with_config(config)` - Create with custom configuration
- `LayoutEngine::layout_circuit(circuit)` - Generate layout for entire circuit
- `LayoutEngine::layout_module(module)` - Generate layout for single module
- `circuit.generate_layout()` - Convenient method on CircuitDescription
- `circuit.generate_layout_with_config(config)` - Generate with custom config

### Circuit Access

- `circuit.main_module()` - Get the main/root module
- `circuit.find_module(name)` - Find module by instance name
- `circuit.find_component_definition(name)` - Find component definition
- `circuit.all_modules()` - Get all modules (flattened)
- `circuit.all_components()` - Get all component instances

### Module Methods

- `module.find_component(name)` - Find component by instance
- `module.find_net(name)` - Find net by name
- `module.find_bus(name)` - Find bus by name
- `module.components()` - Get all components in module
- `module.nested_modules()` - Get nested modules

### Component Methods

- `component.get_param_str(key)` - Get parameter as string
- `component.get_param_number(key)` - Get parameter as number
- `component.has_param(key)` - Check if parameter exists
- `component.is_not_connected()` - Check if marked as NC

## Examples

### Finding Components

```rust
let circuit = CircuitParser::parse_from_file("circuit.json")?;

// Find all resistors
for (module, component) in circuit.all_components() {
    if component.class == "RES" {
        if let Some(resistance) = component.get_param_str("rs") {
            println!("Resistor {}: {}", component.instance, resistance);
        }
    }
}
```

### Analyzing Connections

```rust
if let Some(module) = circuit.find_module("power_supply") {
    for net in &module.nets {
        println!("Net '{}' connects:", net.name);
        for connection in &net.connection {
            println!("  - {}", connection);
        }
    }
}
```

### Parameter Analysis

```rust
for (_, component) in circuit.all_components() {
    if component.class == "CAP" {
        let cap = component.get_param_str("cap").unwrap_or("unknown");
        let volt = component.get_param_str("volt").unwrap_or("unknown");
        println!("Capacitor {}: {} @ {}", component.instance, cap, volt);
    }
}
```

## Error Handling

The parser provides detailed error information:

```rust
match CircuitParser::parse_from_file("circuit.json") {
    Ok(circuit) => {
        // Process circuit
    }
    Err(e) => {
        eprintln!("Parse error: {}", e);
        match e {
            CircuitParseError::JsonError(json_err) => {
                eprintln!("JSON syntax error: {}", json_err);
            }
            CircuitParseError::ValidationError(msg) => {
                eprintln!("Validation failed: {}", msg);
            }
            CircuitParseError::MissingField(field) => {
                eprintln!("Required field missing: {}", field);
            }
            _ => {}
        }
    }
}
```

## Project Structure

The library is organized into several modules for better maintainability:

```
src/
├── lib.rs          # Main library entry point and re-exports
├── error.rs        # Error types and definitions
├── types.rs        # Data structure definitions
├── parser.rs       # Core parsing logic and validation
├── utils.rs        # Convenience methods and utilities
├── tests.rs        # Unit tests
└── layout/         # Layout system
    ├── mod.rs      # Layout module exports
    ├── types.rs    # Layout-specific data structures
    ├── engine.rs   # Main layout engine
    ├── component_layout.rs  # Basic component layout
    ├── chip_layout.rs       # Chip/IC layout
    ├── force_directed.rs    # Force-directed algorithm
    └── tests.rs    # Layout system tests
```

### Module Overview

- **`error`**: Defines `CircuitParseError` and `Result` types
- **`types`**: Contains all data structures (`CircuitDescription`, `Module`, `Component`, etc.)
- **`parser`**: Implements `CircuitParser` with parsing and validation logic
- **`utils`**: Provides convenience methods for accessing and manipulating circuit data
- **`layout`**: Complete layout system with force-directed algorithms
- **`tests`**: Comprehensive unit tests for all functionality

## Running Examples

```bash
# Run the simple test example
cargo run --example simple_test

# Run the basic usage example
cargo run --example basic_usage

# Run the layout example
cargo run --example layout_example

# Run tests
cargo test

# Run layout-specific tests
cargo test layout
```

## Layout System

The layout system uses a force-directed algorithm to automatically place circuit components. For detailed information about the layout system, see [docs/LAYOUT.md](docs/LAYOUT.md).

### Key Features:
- **Automatic component placement** using physics-based algorithms
- **Support for basic components** (R, C, L, etc.) from KiCad component library
- **Automatic chip layout** with configurable pin arrangements
- **Hierarchical layout** for complex multi-module circuits
- **Customizable parameters** for fine-tuning layout behavior

### Layout Algorithm:
1. **Bottom-up recursive processing** of circuit hierarchy
2. **Force-directed placement** using repulsion and attraction forces
3. **Component-specific layout** for different component types
4. **Convergence-based optimization** for stable results

## License

This project is licensed under the MIT License.
