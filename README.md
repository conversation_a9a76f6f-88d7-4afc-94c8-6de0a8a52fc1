# Circuit Description Parser

A Rust library for parsing circuit description files in JSON format. This parser can handle hierarchical circuit designs with modules, components, connections, and parameters.

## Features

- **Hierarchical Design Support**: Parse nested modules and sub-circuits
- **Component Management**: Handle component instances with parameters
- **Connection Validation**: Validate nets, pins, and bus connections
- **Comprehensive Error Handling**: Detailed error messages for debugging
- **Convenient API**: Easy-to-use methods for accessing circuit data
- **Parameter Handling**: Support for component specifications and attributes

## Installation

Add this to your `Cargo.toml`:

```toml
[dependencies]
mclayout = "0.1.0"
```

## Quick Start

```rust
use mclayout::{CircuitParser, Result};

fn main() -> Result<()> {
    // Parse from file
    let circuit = CircuitParser::parse_from_file("circuit.json")?;
    
    // Access the main module
    if let Some(main_module) = circuit.main_module() {
        println!("Main module: {}", main_module.instance);
        
        // Find components
        for component in main_module.components() {
            println!("Component: {} ({})", component.instance, component.class);
        }
    }
    
    Ok(())
}
```

## Circuit Description Format

The parser expects JSON files with the following structure:

```json
{
    "modules": [
        {
            "type": "module",
            "class": "main",
            "instance": "main",
            "objects": [
                {
                    "type": "component",
                    "class": "RES",
                    "instance": "R1",
                    "params": {
                        "rs": "10kΩ",
                        "pkg": "R0603"
                    }
                }
            ],
            "pins": [
                {"name": "VDD", "type": "input"},
                {"name": "GND", "type": "inout"}
            ],
            "nets": [
                {
                    "name": "VDD_NET",
                    "connection": ["R1.1", "VDD"]
                }
            ]
        }
    ],
    "components": [
        {
            "name": "RES",
            "pins": [
                {"id": 1},
                {"id": 2}
            ]
        }
    ]
}
```

## API Reference

### Main Parser

- `CircuitParser::parse_from_file(path)` - Parse from file
- `CircuitParser::parse_from_str(json)` - Parse from JSON string
- `CircuitParser::parse_from_reader(reader)` - Parse from reader
- `CircuitParser::validate_comprehensive(circuit)` - Full validation

### Circuit Access

- `circuit.main_module()` - Get the main/root module
- `circuit.find_module(name)` - Find module by instance name
- `circuit.find_component_definition(name)` - Find component definition
- `circuit.all_modules()` - Get all modules (flattened)
- `circuit.all_components()` - Get all component instances

### Module Methods

- `module.find_component(name)` - Find component by instance
- `module.find_net(name)` - Find net by name
- `module.find_bus(name)` - Find bus by name
- `module.components()` - Get all components in module
- `module.nested_modules()` - Get nested modules

### Component Methods

- `component.get_param_str(key)` - Get parameter as string
- `component.get_param_number(key)` - Get parameter as number
- `component.has_param(key)` - Check if parameter exists
- `component.is_not_connected()` - Check if marked as NC

## Examples

### Finding Components

```rust
let circuit = CircuitParser::parse_from_file("circuit.json")?;

// Find all resistors
for (module, component) in circuit.all_components() {
    if component.class == "RES" {
        if let Some(resistance) = component.get_param_str("rs") {
            println!("Resistor {}: {}", component.instance, resistance);
        }
    }
}
```

### Analyzing Connections

```rust
if let Some(module) = circuit.find_module("power_supply") {
    for net in &module.nets {
        println!("Net '{}' connects:", net.name);
        for connection in &net.connection {
            println!("  - {}", connection);
        }
    }
}
```

### Parameter Analysis

```rust
for (_, component) in circuit.all_components() {
    if component.class == "CAP" {
        let cap = component.get_param_str("cap").unwrap_or("unknown");
        let volt = component.get_param_str("volt").unwrap_or("unknown");
        println!("Capacitor {}: {} @ {}", component.instance, cap, volt);
    }
}
```

## Error Handling

The parser provides detailed error information:

```rust
match CircuitParser::parse_from_file("circuit.json") {
    Ok(circuit) => {
        // Process circuit
    }
    Err(e) => {
        eprintln!("Parse error: {}", e);
        match e {
            CircuitParseError::JsonError(json_err) => {
                eprintln!("JSON syntax error: {}", json_err);
            }
            CircuitParseError::ValidationError(msg) => {
                eprintln!("Validation failed: {}", msg);
            }
            CircuitParseError::MissingField(field) => {
                eprintln!("Required field missing: {}", field);
            }
            _ => {}
        }
    }
}
```

## Project Structure

The parser is organized into several modules for better maintainability:

```
src/
├── lib.rs          # Main library entry point and re-exports
├── error.rs        # Error types and definitions
├── types.rs        # Data structure definitions
├── parser.rs       # Core parsing logic and validation
├── utils.rs        # Convenience methods and utilities
└── tests.rs        # Unit tests
```

### Module Overview

- **`error`**: Defines `CircuitParseError` and `Result` types
- **`types`**: Contains all data structures (`CircuitDescription`, `Module`, `Component`, etc.)
- **`parser`**: Implements `CircuitParser` with parsing and validation logic
- **`utils`**: Provides convenience methods for accessing and manipulating circuit data
- **`tests`**: Comprehensive unit tests for all functionality

## Running Examples

```bash
# Run the simple test example
cargo run --example simple_test

# Run the basic usage example
cargo run --example basic_usage

# Run tests
cargo test
```

## License

This project is licensed under the MIT License.
