* 布局算法自底向上运行（可以考虑用递归算法实现），每一个部件（元器件、芯片、模块等）布局完毕后，应返回：1、模块总体的宽和高；2、所有子组件的坐标（相对于父模块的中心位置）和旋转角度；3、所有引脚的坐标；
* 对基础元器件，如电阻、电容、二极管等，参考事先定义的器件画法，定义文件请见当前项目下的 components 目录，这些器件的画法是从 kicad 转换过来的
* 对于芯片，可以根据引脚的数目，画出矩形，然后在四周排列引脚，矩形的大小、引脚的间距和长度，根据 kicad 的习惯来设置
* 对于每一个模块内子模块的布局，可借助于 Force-Directed Layout 算法的思路：
    - 获取模块的大小和相互之间的连接（忽略模块内部的连接）
    - 模块之间存在斥力。斥力的大小与模块之间的距离相关，靠近时斥力增大，如有重叠则急剧增大，当模块之间的距离大于一定值时，斥力减小到零
    - 模块之间的连接存在拉力。连接是用 net 来表示的，每个net可以有2个或更多的引脚，可以考虑取所有引脚的重心，然后根据重心和引脚的坐标计算拉力，距离越远拉力越大
    - 初始化时可以把所有的子模块放到一个圆上，然后计算所有子模块受到的力，根据受力情况移动子模块，然后重复计算直到收敛
