{"modules": [{"type": "module", "class": "main", "instance": "main", "objects": [{"type": "module", "class": "POWER_USB", "instance": "pwusb", "objects": [{"type": "component", "class": "USB.MINI_SOCKET", "instance": "usb1"}, {"type": "component", "class": "TEST_POINT", "instance": "TP1"}, {"type": "component", "class": "TEST_POINT", "instance": "TP3"}, {"type": "component", "class": "RES", "instance": "RES0", "params": {"rs": "0R", "pkg": "R0603"}}], "pins": [{"name": "POWER_SYS", "type": "inout"}, {"name": "GND", "type": "inout"}], "nets": [{"name": "USB_VBUS_1", "connection": ["usb1.1", "TP1.1", "RES0.1"]}, {"name": "POWER_SYS_RES0", "connection": ["POWER_SYS", "RES0.2"]}, {"name": "GND", "connection": ["usb1.5", "usb1.6", "usb1.7", "usb1.8", "usb1.9", "TP3.1", "GND"]}]}, {"type": "module", "class": "POWER_LDO", "instance": "pwldo", "objects": [{"type": "component", "class": "SGM2019_33YN5G_TR", "instance": "ldo"}, {"type": "component", "class": "CAP", "instance": "main.pwldo.CAP1", "params": {"cap": "10uF", "volt": "10V", "ac": "±20%", "type": "X5R"}}, {"type": "component", "class": "CAP", "instance": "main.pwldo.CAP2", "params": {"cap": "4.7uF", "volt": "6.3V", "ac": "±20%", "pkg": "Y5V"}}, {"type": "component", "class": "TEST_POINT", "instance": "TP2"}], "pins": [{"name": "POWER_SYS", "type": "input"}, {"name": "VDD_3V3", "type": "output"}, {"name": "GND", "type": "inout"}], "nets": [{"name": "POWER_SYS", "connection": ["ldo.1", "ldo.3", "main_POWER_LDO_CAP_1.1"]}, {"name": "GND", "connection": ["main_POWER_LDO_CAP.2", "main_POWER_LDO_CAP_2.2", "ldo.2", "GND"]}, {"name": "VDD_3V3", "connection": ["ldo.5", "main_POWER_LDO_CAP_2.1", "TP2.1"]}]}, {"type": "module", "class": "POWER_DCDC", "instance": "pwdcdc", "objects": [{"type": "component", "class": "LP3220AB5F", "instance": "dcdc"}, {"type": "component", "class": "CAP", "instance": "pwdcdc.CAP0", "params": {"cap": "10uF", "volt": "10V", "ac": "±20%", "type": "X5R"}}, {"type": "component", "class": "RES", "instance": "pwdcdc.RES0", "params": {"rs": "47kΩ"}}, {"type": "component", "class": "CAP", "instance": "pwdcdc.CAP1", "params": {"cap": "1uF", "volt": "10V", "ac": "±10%", "type": "X5R"}}, {"type": "component", "class": "IND", "instance": "pwdcdc.IND0", "params": {"induct": "2.2uH", "amp": "1.5A"}}, {"type": "component", "class": "CAP", "instance": "pwdcdc.CAP2", "params": {"cap": "10uF", "volt": "10V", "ac": "±20%", "type": "X5R"}}, {"type": "component", "class": "CAP", "instance": "pwdcdc.CAP3", "params": {"cap": "100nF", "volt": "25V", "ac": "±20%", "type": "X5R"}}, {"type": "component", "class": "RES", "instance": "pwdcdc.RES1", "params": {"rs": "137kΩ", "ac": "1%"}}, {"type": "component", "class": "RES", "instance": "pwdcdc.RES2", "params": {"rs": "150kΩ", "ac": "1%"}}, {"type": "component", "class": "CAP", "instance": "pwdcdc.CAP4", "params": {"cap": "15pF"}}], "pins": [{"name": "VDD_3V3", "type": "input"}, {"name": "VCC_1V2", "type": "output"}, {"name": "GND", "type": "inout"}], "nets": [{"name": "VDD_3V3", "connection": ["dcdc.4", "POWER_DCDC_CAP_VIN.1", "POWER_DCDC_RES_EN.1"]}, {"name": "GND", "connection": ["POWER_DCDC_CAP_VIN.2", "POWER_DCDC_CAP_OUT1.2", "POWER_DCDC_CAP_OUT2.2", "dcdc.2", "POWER_DCDC_CAP_EN.2", "POWER_DCDC_RES_FB2.2"]}, {"name": "", "connection": ["POWER_DCDC_RES_EN.2", "dcdc.1", "POWER_DCDC_CAP_EN.1"]}, {"name": "", "connection": ["dcdc.3", "POWER_DCDC_IND.1"]}, {"name": "VCC_1V2", "connection": ["POWER_DCDC_IND.2", "POWER_DCDC_CAP_OUT1.1", "POWER_DCDC_CAP_OUT2.1", "POWER_DCDC_RES_FB1.1", "POWER_DCDC_CAP_FB.2"]}, {"name": "", "connection": ["POWER_DCDC_RES_FB1.2", "dcdc.5", "POWER_DCDC_RES_FB2.1", "POWER_DCDC_CAP_FB.1"]}]}, {"type": "module", "class": "US513", "instance": "mcu", "objects": [{"type": "component", "class": "US513_20_F", "instance": "uC"}, {"type": "component", "class": "CAP", "instance": "US513_CAP_VDD_IO", "params": {"cap": "1uF", "volt": "10V", "ac": "±10%", "type": "X5R"}}, {"type": "component", "class": "CAP", "instance": "US513_CAP_VDD_CORE", "params": {"cap": "1uF", "volt": "10V", "ac": "±10%", "type": "X5R"}}, {"type": "component", "class": "CAP", "instance": "US513_CAP_AVDD", "params": {"cap": "1uF", "volt": "10V", "ac": "±10%", "type": "X5R"}}, {"type": "component", "class": "RES", "instance": "US513_RES_GPIO2_PU", "params": {"rs": "100kΩ"}}, {"type": "component", "class": "RES", "instance": "US513_RES_GPIO2_PD", "params": {"rs": "100kΩ", "connection": "NC"}}, {"type": "component", "class": "DST310S", "instance": "crystal", "params": {"pkg": "X6", "connection": "NC"}}, {"type": "component", "class": "RES", "instance": "R442", "params": {"rs": "1MΩ", "ac": "±1%", "pkg": "R0402", "connection": "NC"}}, {"type": "component", "class": "CAP", "instance": "US513_CAP_XTAL1", "params": {"cap": "18pF", "volt": "50V", "ac": "±5%", "pkg": "C0402", "connection": "NC"}}, {"type": "component", "class": "CAP", "instance": "US513_CAP_XTAL2", "params": {"cap": "18pF", "volt": "50V", "ac": "±5%", "pkg": "C0402", "connection": "NC"}}, {"type": "component", "class": "CAP", "instance": "US513_CAP_MIC_P", "params": {"cap": "1uF", "volt": "10V", "ac": "±10%", "type": "X5R"}}, {"type": "component", "class": "CAP", "instance": "US513_CAP_MIC_N", "params": {"cap": "1uF", "volt": "10V", "ac": "±10%", "type": "X5R"}}, {"type": "component", "class": "RES", "instance": "US513_RES_I2C_SDA", "params": {"rs": "10kΩ"}}, {"type": "component", "class": "RES", "instance": "US513_RES_I2C_SCL", "params": {"rs": "10kΩ"}}, {"type": "component", "class": "RES", "instance": "US513_RES_UART0_TX", "params": {"rs": "0Ω"}}, {"type": "component", "class": "RES", "instance": "US513_RES_UART0_RX", "params": {"rs": "0Ω"}}, {"type": "component", "class": "RES", "instance": "US513_RES_PDM_PU", "params": {"rs": "100kΩ"}}, {"type": "series", "instance": "SERIES_CAP_RES", "member": [{"type": "component", "class": "CAP", "instance": "US513_CAP_DAC1", "params": {"cap": "2.2uF", "volt": "25V", "ac": "±20%", "type": "X5R"}}, {"type": "component", "class": "RES", "instance": "US513_RES_DAC", "params": {"rs": "15kΩ", "ac": "±1%"}}]}, {"type": "component", "class": "CAP", "instance": "US513_CAP_DAC_FILT1", "params": {"cap": "1nF", "volt": "25V", "ac": "±20%", "type": "X5R"}}, {"type": "component", "class": "RES", "instance": "US513_RES_DAC_FILT", "params": {"rs": "10kΩ"}}, {"type": "component", "class": "CAP", "instance": "US513_CAP_DAC_OUT", "params": {"cap": "330nF", "volt": "10V", "ac": "X5R"}}], "pins": ["VDD_3V3", "VCC_1V2", "GND", "MIC", "I2C0", "SPI", "UART0", "UART1", "dac", "US_SPEAKER_MUTE"], "buses": [{"name": "MIC", "signals": ["P", "N"]}, {"name": "I2C0", "signals": ["SDA", "SCL"]}, {"name": "SPI", "signals": ["MISO", "MOSI", "CS", "SCLK"]}, {"name": "UART0", "signals": ["TX", "RX"]}, {"name": "UART1", "signals": ["TX", "RX"]}], "nets": [{"name": "VDD_3V3", "connection": ["uC.5", "US513_CAP_VDD_IO.1", "US513_RES_GPIO2_PU.1", "US513_RES_I2C_SDA.2", "US513_RES_I2C_SCL.2", "US513_RES_PDM_PU.2"]}, {"name": "VCC_1V2", "connection": ["uC.14", "US513_CAP_VDD_CORE.1"]}, {"name": "", "connection": ["uC.15", "US513_CAP_AVDD.1"]}, {"name": "", "connection": ["US513_RES_GPIO2_PU.2", "uC.20", "US513_RES_GPIO2_PD.1"]}, {"name": "", "connection": ["crystal.1", "US513_CAP_XTAL1.1", "R442.1", "uC.4"]}, {"name": "", "connection": ["crystal.2", "US513_CAP_XTAL2.1", "R442.2", "uC.3"]}, {"name": "MIC", "type": "bus", "signals": [{"name": "P", "connection": ["US513_CAP_MIC_P.1"]}, {"name": "N", "connection": ["US513_CAP_MIC_N.1"]}]}, {"name": "", "connection": ["US513_CAP_MIC_P.2", "uC.16"]}, {"name": "", "connection": ["US513_CAP_MIC_N.2", "uC.17"]}, {"type": "bus", "name": "I2C0", "signals": [{"name": "SDA", "connection": ["US513_RES_I2C_SDA.1", "uC.1"]}, {"name": "SCL", "connection": ["US513_RES_I2C_SCL.1", "uC.2"]}]}, {"type": "bus", "name": "SPI", "signals": [{"name": "SCLK", "connection": ["uC.8"]}, {"name": "MOSI", "connection": ["uC.9"]}, {"name": "CSN", "connection": ["uC.10"]}, {"name": "MISO", "connection": ["uC.11"]}]}, {"type": "bus", "name": "UART0", "signals": [{"name": "TX", "connection": ["US513_RES_UART0_TX.1"]}, {"name": "RX", "connection": ["US513_RES_UART0_RX.1"]}]}, {"name": "", "connection": ["US513_RES_UART0_TX.2", "uC.6", "SERIES_CAP_RES.1"]}, {"name": "", "connection": ["US513_RES_UART0_RX.2", "uC.7", "US513_RES_PDM_PU.1"]}, {"name": "", "connection": ["SERIES_CAP_RES.2", "US513_CAP_DAC_FILT1.1", "US513_RES_DAC_FILT.1", "US513_CAP_DAC_OUT.1"]}, {"name": "dac", "connection": ["US513_CAP_DAC_OUT.2"]}, {"name": "US_SPEAKER_MUTE", "connection": ["uC.19"]}, {"name": "GND", "connection": ["US513_CAP_VDD_IO.2", "US513_CAP_VDD_CORE.2", "US513_CAP_AVDD.2", "US513_RES_GPIO2_PD.2", "US513_CAP_XTAL1.2", "US513_CAP_XTAL2.2", "US513_CAP_DAC_FILT1.2", "US513_RES_DAC_FILT.2", "uC.21"]}]}, {"type": "component", "class": "GD25Q32E", "instance": "flash"}, {"type": "module", "class": "MIC_SIP", "instance": "mdlmic0", "objects": [{"type": "component", "class": "RES", "instance": "MIC_SIP_RES_VMIC", "params": {"rs": "240Ω", "ac": "±1%", "pkg": "R0402"}}, {"type": "component", "class": "CAP", "instance": "MIC_SIP_CAP_VMIC", "params": {"cap": "4.7uF", "volt": "6.3V", "ac": "±20%", "pkg": "Y5V"}}, {"type": "component", "class": "MICROPHONE.SIP2", "instance": "mic0"}, {"type": "component", "class": "CAP", "instance": "MIC_SIP_CAP_DIFF", "params": {"cap": "470pF", "volt": "10V", "ac": "±20%", "type": "X5R"}}, {"type": "component", "class": "DIO.ESD", "instance": "MIC_SIP_ESD1", "params": {"partno": "ESD9B5V-2/TR", "connection": "NC"}}, {"type": "component", "class": "DIO.ESD", "instance": "MIC_SIP_ESD2", "params": {"partno": "ESD9B5V-2/TR", "connection": "NC"}}, {"type": "component", "class": "CAP", "instance": "C183", "params": {"cap": "1uF", "volt": "10V", "ac": "±10%", "type": "X5R"}}, {"type": "component", "class": "RES", "instance": "MIC_SIP_RES1", "params": {"rs": "1kΩ", "a c": "±1%", "pkg": "R0402"}}, {"type": "component", "class": "RES", "instance": "MIC_SIP_RES2", "params": {"rs": "1kΩ", "ac": "±1%", "pkg": "R0402"}}, {"type": "component", "class": "RES", "instance": "MIC_SIP_RES3", "params": {"rs": "1kΩ", "ac": "±1%", "pkg": "R0402"}}, {"type": "component", "class": "RES", "instance": "MIC_SIP_RES4", "params": {"rs": "1kΩ", "ac": "±1%", "pkg": "R0402"}}, {"type": "component", "class": "MICROPHONE.WM7121P", "instance": "wm7121", "params": {"connection": "NC"}}, {"type": "component", "class": "CAP", "instance": "MIC_SIP_CAP_WM", "params": {"cap": "100nF", "volt": "25V", "ac": "±20%", "type": "X5R", "pkg": "C0402", "connection": "NC"}}, {"type": "component", "class": "RES", "instance": "MIC_SIP_RES_NC", "params": {"rs": "0Ω", "connection": "NC"}}], "pins": ["VDD_3V3", "GND", "MIC"], "nets": [{"name": "VDD_3V3", "connection": ["MIC_SIP_RES_VMIC.1", "wm7121.4", "MIC_SIP_CAP_WM.1"]}, {"name": "VMIC", "connection": ["MIC_SIP_RES_VMIC.2", "MIC_SIP_CAP_VMIC.1", "MIC_SIP_RES1.1"]}, {"name": "MIC.P", "connection": ["mic0.1", "MIC_SIP_CAP_DIFF.1", "MIC_SIP_ESD1.1", "MIC_SIP_RES2.2"]}, {"name": "MIC.N", "connection": ["mic0.2", "MIC_SIP_CAP_DIFF.2", "MIC_SIP_ESD2.1", "MIC_SIP_RES4.2", "MIC_SIP_RES_NC.1"]}, {"name": "", "connection": ["MIC_SIP_RES1.2", "C183.1", "MIC_SIP_RES2.1"]}, {"name": "", "connection": ["MIC_SIP_RES3.1", "C183.2", "MIC_SIP_RES4.1"]}, {"name": "GND", "connection": ["MIC_SIP_CAP_VMIC.2", "MIC_SIP_ESD1.2", "MIC_SIP_ESD2.2", "MIC_SIP_RES3.2", "wm7121.2", "wm7121.3", "MIC_SIP_CAP_WM.2", "MIC_SIP_RES_NC.2", "mic0.3", "mic0.4"]}]}, {"type": "module", "class": "SPEAKER_M", "instance": "spk", "objects": [{"type": "component", "class": "LPA4871", "instance": "lpa"}, {"type": "component", "class": "SPEAKER.PHB2AWB", "instance": "spk"}, {"type": "component", "class": "CAP", "instance": "SPEAKER_M_CAP_VDD", "params": {"cap": "10uF", "volt": "10V", "ac": "±20%", "type": "X5R", "unknown-param-3": "C0603"}}, {"type": "component", "class": "RES", "instance": "SPEAKER_M_RES_EN", "params": {"rs": "10kΩ"}}, {"type": "component", "class": "CAP", "instance": "SPEAKER_M_CAP_BYPASS", "params": {"cap": "1uF", "volt": "10V", "ac": "±10%", "type": "X5R", "unknown-param-3": "C0402"}}, {"type": "component", "class": "RES", "instance": "SPEAKER_M_RES_IN", "params": {"rs": "15kΩ", "ac": "±1%"}}, {"type": "component", "class": "RES", "instance": "SPEAKER_M_RES_FB", "params": {"rs": "30kΩ", "ac": "±1%"}}, {"type": "component", "class": "TEST_POINT", "instance": "SPEAKER_M_TP1"}, {"type": "component", "class": "TEST_POINT", "instance": "SPEAKER_M_TP2"}, {"type": "component", "class": "DIO.ESD", "instance": "SPEAKER_M_ESD1", "params": {"partno": "ESD9B5V-2/TR", "connection": "NC"}}, {"type": "component", "class": "DIO.ESD", "instance": "SPEAKER_M_ESD2", "params": {"partno": "ESD9B5V-2/TR", "connection": "NC"}}], "pins": ["VDD_3V3", "GND", "DAC_OUT", "US_SPEAKER_MUTE"], "nets": [{"name": "VDD_3V3", "connection": ["lpa.6", "SPEAKER_M_CAP_VDD.1", "SPEAKER_M_RES_EN.1"]}, {"name": "US_SPEAKER_MUTE", "connection": ["SPEAKER_M_RES_EN.2", "lpa.1"]}, {"name": "", "connection": ["lpa.2", "lpa.3", "SPEAKER_M_CAP_BYPASS.1"]}, {"name": "", "connection": ["DAC_OUT", "SPEAKER_M_RES_IN.1"]}, {"name": "", "connection": ["SPEAKER_M_RES_IN.2", "lpa.4", "SPEAKER_M_RES_FB.1"]}, {"name": "SPK.N", "connection": ["SPEAKER_M_RES_FB.2", "lpa.5", "SPEAKER_M_TP2.1", "SPEAKER_M_ESD2.1", "spk.2"]}, {"name": "SPK.P", "connection": ["lpa.8", "SPEAKER_M_TP1.1", "SPEAKER_M_ESD1.1", "spk.1"]}, {"name": "GND", "connection": ["SPEAKER_M_CAP_VDD.2", "SPEAKER_M_CAP_BYPASS.2", "lpa.7", "spk.3", "spk.4", "SPEAKER_M_ESD1.2", "SPEAKER_M_ESD2.2", "GND"]}]}], "buses": [{"name": "V5V", "labels": ["POWER_SYS", "GND"]}, {"name": "V3V3", "labels": ["VDD_3V3", "GND"]}, {"name": "V1V2", "labels": ["VCC_1V2", "GND"]}], "nets": [{"name": "V5V.POWER_SYS", "connection": ["pwusb.POWER_SYS", "pwldo.POWER_SYS"]}, {"name": "GND", "connection": ["pwusb.GND", "pwldo.GND", "pwdcdc.GND", "mcu.GND", "flash.GND", "mic0.GND", "spk.GND"]}, {"name": "V3V3.VDD_3V3", "connection": ["pwldo.VDD_3V3", "pwdcdc.VDD_3V3", "mcu.VDD_3V3", "flash.v3v3", "mic0.VDD_3V3", "spk.VDD_3V3"]}, {"name": "V1V2.VCC_1V2", "connection": ["pwdcdc.VCC_1V2", "mcu.VCC_1V2"]}, {"type": "bus", "name": "SPI", "signals": [{"name": "SCLK", "connection": ["mcu.SPI.SCLK", "flash.SPI.SCLK"]}, {"name": "MISO", "connection": ["mcu.SPI.MISO", "flash.SPI.MOSI"]}, {"name": "CSN", "connection": ["mcu.SPI.CSN", "flash.SPI.CS"]}, {"name": "MOSI", "connection": ["mcu.SPI.MOSI", "flash.SPI.MISO"]}]}, {"type": "bus", "name": "MIC", "signals": [{"name": "P", "connection": ["mic.MIC.P", "mcu.MIC.P"]}, {"name": "N", "connection": ["mic.MIC.N", "mcu.MIC.N"]}]}, {"name": "DAC_SIGNAL", "connection": ["mcu.dac", "spk.DAC_OUT"]}, {"name": "SPEAKER_MUTE", "connection": ["mcu.US_SPEAKER_MUTE", "spk.US_SPEAKER_MUTE"]}]}], "components": [{"name": "GD25Q32E", "partno": "GD25Q32ESIG", "package": "SOP8", "objects": [{"type": "component", "class": "RES", "instance": "RES0", "params": {"rs": "10kΩ"}}, {"type": "component", "class": "RES", "instance": "RES2", "params": {"rs": "10kΩ"}}, {"type": "component", "class": "RES", "instance": "RES3", "params": {"rs": "10kΩ"}}, {"type": "component", "class": "CAP", "instance": "CAP0", "params": {"cap": "100nF", "volt": "25V", "ac": "±20%", "type": "X5R"}}], "buses": [{"name": "SPI", "signals": ["CS", "MISO", "MOSI", "SCLK"]}], "pins": [{"id": 1, "name": ["_CS"]}, {"id": 2, "name": ["SO", "IO1"]}, {"id": 3, "name": ["_WP", "IO2"]}, {"id": 4, "name": ["VSS"]}, {"id": 5, "name": ["SI", "IO0"]}, {"id": 6, "name": ["SCLK"]}, {"id": 7, "name": ["_HOLD", "IO3"]}, {"id": 8, "name": ["VCC"]}], "nets": [{"name": "", "connection": [1, "RES0.1"]}, {"name": "", "connection": ["RES0.2", "v3v3"]}, {"name": "", "connection": [3, "RES2.1"]}, {"name": "", "connection": ["RES2.2", "v3v3"]}, {"name": "", "connection": [7, "RES3.1"]}, {"name": "", "connection": ["RES3.2", "v3v3"]}, {"name": "", "connection": [8, "CAP0.1"]}, {"name": "", "connection": ["CAP0.2", "GND"]}]}, {"class": "mc.res.RES", "attr": {"alias": "Resistor", "spec": {"resistance": "{rs}", "accuracy": "{ac}"}, "package": "{pkg}"}, "pins": [{"id": 1}, {"id": 2}]}, {"name": "mc.cap.CAP", "attr": {"alias": "Capacitor", "spec": {"capacitance": "{cap}", "accuracy": "{ac}", "voltage": "{volt}"}, "package": "{pkg}"}, "pins": [{"id": 1}, {"id": 2}]}, {"name": "USB.MINI_SOCKET", "attr": {"partno": "HUM011D-5, package: USB MINI SOCKET"}, "pins": [{"id": 1, "name": ["VBUS"]}, {"id": 2, "name": ["D-"]}, {"id": 3, "name": ["D+"]}, {"id": 4, "name": ["ID"]}, {"id": 5, "name": ["GND"]}, {"id": 6, "name": ["GND"]}, {"id": 7, "name": ["GND"]}, {"id": 8, "name": ["SHIELD3"]}, {"id": 9, "name": ["SHIELD4"]}], "nets": [{"name": "GND", "connection": [5, 6, 7]}]}, {"name": "TEST_POINT", "pins": [{"id": 1}]}, {"name": "SGM2019_33YN5G_TR", "attr": {"partno": "SGM2019-3.3YN5G/TR, package: SOT23-5"}, "pins": [{"id": 1, "name": ["Vin"], "attr": {"unknown-attr-1": "2.5V~5.5V"}}, {"id": 2, "name": ["GND"]}, {"id": 3, "name": ["CE"]}, {"id": 4, "name": ["FB"]}, {"id": 5, "name": ["Vout"]}]}, {"name": "LP3220AB5F", "attr": {"partno": "LP3220AB5F, package: SOT23-5"}, "pins": [{"id": 1, "name": ["EN"]}, {"id": 2, "name": ["GND"]}, {"id": 3, "name": ["LX"], "attr": {"unknown-attr-1": "0.6V~5.5V"}}, {"id": 4, "name": ["Vin"], "attr": {"unknown-attr-1": "2.5V~5.5V"}}, {"id": 5, "name": ["FB"]}]}, {"name": "IND", "alias": "Inductor", "spec": {"inductance": "{induct}"}, "pins": [{"id": 1}, {"id": 2}]}, {"name": "US513_20_F", "attr": {"partno": "US513_20_F, package: QFN20"}, "pins": [{"id": 1, "name": ["I2C0.SDA", "GPIO.3"]}, {"id": 2, "name": ["I2C0.SCL", "GPIO.4"]}, {"id": 3, "name": ["XTAL.XIN"]}, {"id": 4, "name": ["XTAL.XOUT"]}, {"id": 5, "name": ["VDD_IO"], "attr": {"unknown-attr-1": "3.3V"}}, {"id": 6, "name": ["UART0.TX", "I2C1.SDA", "GPIO.5"]}, {"id": 7, "name": ["UART0.RX", "I2C1.SCL", "GPIO.6"]}, {"id": 8, "name": ["PDM.CLK", "GPIO.7", "SPI.SCLK"]}, {"id": 9, "name": ["PDM.DATA", "GPIO.8", "SPI.MOSI"]}, {"id": 10, "name": ["I2C1.SDA", "GPIO.9", "SPI.CSN"]}, {"id": 11, "name": ["I2C1.SCL", "GPIO.10", "SPI.MISO"]}, {"id": 12, "name": ["UART1.TX", "GPIO.5"]}, {"id": 13, "name": ["UART1.RX", "GPIO.6"]}, {"id": 14, "name": ["VDD_CORE"], "attr": {"unknown-attr-1": "1.2V"}}, {"id": 15, "name": ["AVDD09_CAP"]}, {"id": 16, "name": ["ADC.P"]}, {"id": 17, "name": ["ADC.N"]}, {"id": 18, "name": ["JTAG.TMS", "GPIO.0", "I2S.IN_BCLK_OUT"]}, {"id": 19, "name": ["JTAG.CLK", "GPIO.1", "I2S.IN_DATA_IN"]}, {"id": 20, "name": ["GPIO.2", "EXT_CLK_IN"]}, {"id": 21, "name": ["GND"]}], "buses": [{"name": "SPI", "signals": ["SCLK", "MOSI", "CSN", "MISO"]}]}, {"name": "DST310S", "partno": "DST310S", "package": "3215", "spec": {"frequency": "32kHz"}, "pins": [{"id": 1, "name": ["XTAL.X1"]}, {"id": 2, "name": ["XTAL.X2"]}], "buses": [{"name": "XTAL", "signals": ["X1", "X2"]}]}, {"name": "MICROPHONE.SIP2", "attr": {"partno": "SIP2-1.25MM-WA, package: "}, "pins": [{"id": 1, "name": ["P"]}, {"id": 2, "name": ["N"]}, {"id": 3, "name": ["GND"]}, {"id": 4, "name": ["GND"]}]}, {"name": "MICROPHONE.WM7121P", "attr": {"partno": "WM7121P, package: "}, "pins": [{"id": 1, "name": ["P"]}, {"id": 2, "name": ["GND"]}, {"id": 3, "name": ["GND"]}, {"id": 4, "name": ["VCC"]}]}, {"name": "DIO.ESD", "attr": {"partno": "{partno}"}, "pins": [{"id": 1}, {"id": 2}]}, {"name": "LPA4871", "attr": {"partno": "LPA4871, package: SOP8"}, "pins": [{"id": 1, "name": ["EN"]}, {"id": 2, "name": ["BYPASS"]}, {"id": 3, "name": ["IN.P"]}, {"id": 4, "name": ["IN.N"]}, {"id": 5, "name": ["VO1"]}, {"id": 6, "name": ["VDD"]}, {"id": 7, "name": ["GND"]}, {"id": 8, "name": ["VO2"]}], "buses": [{"name": "IN", "signals": ["P", "N"]}]}, {"name": "SPEAKER.PHB2AWB", "attr": {"partno": "PHB-2AWB, package: "}, "pins": [{"id": 1, "name": ["P"]}, {"id": 2, "name": ["N"]}, {"id": 3, "name": ["GND"]}, {"id": 4, "name": ["GND"]}]}]}