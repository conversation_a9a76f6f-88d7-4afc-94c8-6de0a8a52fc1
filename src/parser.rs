use crate::types::*;
use crate::error::*;
use std::collections::HashSet;

/// Circuit parser for loading and parsing circuit description files
pub struct CircuitParser;

impl CircuitParser {
    /// Create a new circuit parser
    pub fn new() -> Self {
        Self
    }

    /// Parse a circuit description from a JSON string
    pub fn parse_from_str(json_str: &str) -> Result<CircuitDescription> {
        let circuit: CircuitDescription = serde_json::from_str(json_str)?;
        Self::validate_circuit(&circuit)?;
        Ok(circuit)
    }

    /// Parse a circuit description from a file
    pub fn parse_from_file<P: AsRef<std::path::Path>>(path: P) -> Result<CircuitDescription> {
        let content = std::fs::read_to_string(path)?;
        Self::parse_from_str(&content)
    }

    /// Parse a circuit description from a reader
    pub fn parse_from_reader<R: std::io::Read>(reader: R) -> Result<CircuitDescription> {
        let circuit: CircuitDescription = serde_json::from_reader(reader)?;
        Self::validate_circuit(&circuit)?;
        Ok(circuit)
    }

    /// Validate the parsed circuit description
    fn validate_circuit(circuit: &CircuitDescription) -> Result<()> {
        // Validate that we have at least one module
        if circuit.modules.is_empty() {
            return Err(CircuitParseError::ValidationError(
                "Circuit must contain at least one module".to_string(),
            ));
        }

        // Validate each module
        for module in &circuit.modules {
            Self::validate_module(module)?;
        }

        // Validate component definitions
        for component in &circuit.components {
            Self::validate_component_definition(component)?;
        }

        Ok(())
    }

    /// Validate a module
    fn validate_module(module: &Module) -> Result<()> {
        if module.class.is_empty() {
            return Err(CircuitParseError::MissingField("module.class".to_string()));
        }
        if module.instance.is_empty() {
            return Err(CircuitParseError::MissingField("module.instance".to_string()));
        }

        // Validate nested objects
        for object in &module.objects {
            match object {
                CircuitObject::Module(nested_module) => {
                    Self::validate_module(nested_module)?;
                }
                CircuitObject::Component(component) => {
                    Self::validate_component(component)?;
                }
                CircuitObject::Series(series) => {
                    Self::validate_series_group(series)?;
                }
            }
        }

        Ok(())
    }

    /// Validate a component instance
    fn validate_component(component: &Component) -> Result<()> {
        if component.class.is_empty() {
            return Err(CircuitParseError::MissingField("component.class".to_string()));
        }
        if component.instance.is_empty() {
            return Err(CircuitParseError::MissingField("component.instance".to_string()));
        }
        Ok(())
    }

    /// Validate a series group
    fn validate_series_group(series: &SeriesGroup) -> Result<()> {
        if series.instance.is_empty() {
            return Err(CircuitParseError::MissingField("series.instance".to_string()));
        }
        if series.member.is_empty() {
            return Err(CircuitParseError::ValidationError(
                "Series group must contain at least one member".to_string(),
            ));
        }
        for member in &series.member {
            Self::validate_component(member)?;
        }
        Ok(())
    }

    /// Validate a component definition
    fn validate_component_definition(component: &ComponentDefinition) -> Result<()> {
        // Component definitions can have either name or class, but should have some identifier
        if component.name.is_empty() && component.class.is_empty() {
            return Err(CircuitParseError::ValidationError(
                "Component definition must have either name or class".to_string(),
            ));
        }
        Ok(())
    }
}

impl Default for CircuitParser {
    fn default() -> Self {
        Self::new()
    }
}

/// Additional validation and utility methods
impl CircuitParser {
    /// Validate net connections and references
    pub fn validate_connections(circuit: &CircuitDescription) -> Result<()> {
        for module in &circuit.modules {
            Self::validate_module_connections(module, &circuit.components)?;
        }
        Ok(())
    }

    /// Validate connections within a module
    fn validate_module_connections(module: &Module, component_defs: &[ComponentDefinition]) -> Result<()> {
        // Collect all available connection points
        let mut available_pins = HashSet::new();
        
        // Add module pins
        match &module.pins {
            ModulePins::Simple(pin_names) => {
                for name in pin_names {
                    available_pins.insert(name.clone());
                }
            }
            ModulePins::Detailed(pins) => {
                for pin in pins {
                    match pin {
                        ModulePin::Simple(name) => {
                            available_pins.insert(name.clone());
                        }
                        ModulePin::Detailed { name, .. } => {
                            available_pins.insert(name.clone());
                        }
                    }
                }
            }
        }

        // Add component pins from objects
        for object in &module.objects {
            match object {
                CircuitObject::Component(component) => {
                    // Find component definition to get pin information
                    if let Some(comp_def) = component_defs.iter().find(|def| 
                        def.name == component.class || def.class == component.class) {
                        for pin in &comp_def.pins {
                            let pin_ref = format!("{}.{}", component.instance, pin.id);
                            available_pins.insert(pin_ref);
                        }
                    }
                }
                CircuitObject::Series(series) => {
                    // Add series connection points
                    available_pins.insert(format!("{}.1", series.instance));
                    available_pins.insert(format!("{}.2", series.instance));
                }
                CircuitObject::Module(nested_module) => {
                    // Recursively validate nested modules
                    Self::validate_module_connections(nested_module, component_defs)?;
                }
            }
        }

        // Validate net connections
        for net in &module.nets {
            for connection in &net.connection {
                if !connection.is_empty() && !available_pins.contains(connection) {
                    // Check if it's a bus signal reference
                    if !connection.contains('.') && !Self::is_valid_bus_reference(connection, module) {
                        return Err(CircuitParseError::ValidationError(
                            format!("Invalid connection reference: {} in module {}", connection, module.instance)
                        ));
                    }
                }
            }

            // Validate bus signals
            for signal in &net.signals {
                for connection in &signal.connection {
                    if !connection.is_empty() && !available_pins.contains(connection) {
                        return Err(CircuitParseError::ValidationError(
                            format!("Invalid bus signal connection: {} in net {}", connection, net.name)
                        ));
                    }
                }
            }
        }

        Ok(())
    }

    /// Check if a connection reference is a valid bus reference
    fn is_valid_bus_reference(connection: &str, module: &Module) -> bool {
        // Check if it's a module pin
        match &module.pins {
            ModulePins::Simple(pin_names) => {
                if pin_names.contains(&connection.to_string()) {
                    return true;
                }
            }
            ModulePins::Detailed(pins) => {
                for pin in pins {
                    match pin {
                        ModulePin::Simple(name) => {
                            if name == connection {
                                return true;
                            }
                        }
                        ModulePin::Detailed { name, .. } => {
                            if name == connection {
                                return true;
                            }
                        }
                    }
                }
            }
        }

        // Check if it's a bus name
        module.buses.iter().any(|bus| bus.name == connection)
    }

    /// Validate parameter values
    pub fn validate_parameters(circuit: &CircuitDescription) -> Result<()> {
        for module in &circuit.modules {
            Self::validate_module_parameters(module)?;
        }
        Ok(())
    }

    /// Validate parameters within a module
    fn validate_module_parameters(module: &Module) -> Result<()> {
        for object in &module.objects {
            match object {
                CircuitObject::Component(component) => {
                    Self::validate_component_parameters(component)?;
                }
                CircuitObject::Series(series) => {
                    for member in &series.member {
                        Self::validate_component_parameters(member)?;
                    }
                }
                CircuitObject::Module(nested_module) => {
                    Self::validate_module_parameters(nested_module)?;
                }
            }
        }
        Ok(())
    }

    /// Validate component parameters
    fn validate_component_parameters(component: &Component) -> Result<()> {
        // Check for common parameter issues
        for (key, value) in &component.params {
            // Check for malformed parameter values
            if let Some(str_val) = value.as_str() {
                if str_val.trim().is_empty() {
                    return Err(CircuitParseError::ValidationError(
                        format!("Empty parameter value for {} in component {}", key, component.instance)
                    ));
                }
                
                // Check for common typos in parameter names
                if key.contains(' ') && !key.starts_with("unknown-") {
                    return Err(CircuitParseError::ValidationError(
                        format!("Parameter name contains spaces: {} in component {}", key, component.instance)
                    ));
                }
            }
        }
        Ok(())
    }

    /// Perform comprehensive validation
    pub fn validate_comprehensive(circuit: &CircuitDescription) -> Result<()> {
        Self::validate_circuit(circuit)?;
        Self::validate_connections(circuit)?;
        Self::validate_parameters(circuit)?;
        Ok(())
    }
}
