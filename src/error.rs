use thiserror::Error;

/// Errors that can occur during circuit parsing
#[derive(Erro<PERSON>, Debug)]
pub enum CircuitParseError {
    #[error("JSON parsing error: {0}")]
    JsonError(#[from] serde_json::Error),
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    #[error("Validation error: {0}")]
    ValidationError(String),
    #[error("Missing required field: {0}")]
    MissingField(String),
}

/// Type alias for Result with CircuitParseError
pub type Result<T> = std::result::Result<T, CircuitParseError>;
