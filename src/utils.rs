use crate::types::*;

/// Convenience methods for accessing circuit data
impl CircuitDescription {
    /// Get the main/root module
    pub fn main_module(&self) -> Option<&Module> {
        self.modules.iter().find(|m| m.class == "main" || m.instance == "main")
    }

    /// Find a module by instance name
    pub fn find_module(&self, instance: &str) -> Option<&Module> {
        for module in &self.modules {
            if let Some(found) = self.find_module_in_tree(module, instance) {
                return Some(found);
            }
        }
        None
    }

    /// Search for a module within a module tree
    fn find_module_in_tree<'a>(&self, module: &'a Module, instance: &str) -> Option<&'a Module> {
        if module.instance == instance {
            return Some(module);
        }
        
        // Search in nested modules
        for object in &module.objects {
            if let CircuitObject::Module(nested_module) = object {
                if let Some(found) = self.find_module_in_tree(nested_module, instance) {
                    return Some(found);
                }
            }
        }
        None
    }

    /// Find a component definition by name or class
    pub fn find_component_definition(&self, name_or_class: &str) -> Option<&ComponentDefinition> {
        self.components.iter().find(|comp| 
            comp.name == name_or_class || comp.class == name_or_class
        )
    }

    /// Get all modules flattened into a list
    pub fn all_modules(&self) -> Vec<&Module> {
        let mut modules = Vec::new();
        for module in &self.modules {
            self.collect_modules_from_tree(module, &mut modules);
        }
        modules
    }

    /// Recursively collect all modules from a module tree
    fn collect_modules_from_tree<'a>(&self, module: &'a Module, collector: &mut Vec<&'a Module>) {
        collector.push(module);
        for object in &module.objects {
            if let CircuitObject::Module(nested_module) = object {
                self.collect_modules_from_tree(nested_module, collector);
            }
        }
    }

    /// Get all component instances across all modules
    pub fn all_components(&self) -> Vec<(&Module, &Component)> {
        let mut components = Vec::new();
        for module in &self.modules {
            self.collect_components_from_tree(module, &mut components);
        }
        components
    }

    /// Recursively collect all component instances from a module tree
    fn collect_components_from_tree<'a>(&self, module: &'a Module, collector: &mut Vec<(&'a Module, &'a Component)>) {
        for object in &module.objects {
            match object {
                CircuitObject::Component(component) => {
                    collector.push((module, component));
                }
                CircuitObject::Series(series) => {
                    for member in &series.member {
                        collector.push((module, member));
                    }
                }
                CircuitObject::Module(nested_module) => {
                    self.collect_components_from_tree(nested_module, collector);
                }
            }
        }
    }
}

/// Convenience methods for Module
impl Module {
    /// Find a component by instance name within this module
    pub fn find_component(&self, instance: &str) -> Option<&Component> {
        for object in &self.objects {
            match object {
                CircuitObject::Component(component) => {
                    if component.instance == instance {
                        return Some(component);
                    }
                }
                CircuitObject::Series(series) => {
                    for member in &series.member {
                        if member.instance == instance {
                            return Some(member);
                        }
                    }
                }
                _ => {}
            }
        }
        None
    }

    /// Find a net by name within this module
    pub fn find_net(&self, name: &str) -> Option<&Net> {
        self.nets.iter().find(|net| net.name == name)
    }

    /// Find a bus by name within this module
    pub fn find_bus(&self, name: &str) -> Option<&Bus> {
        self.buses.iter().find(|bus| bus.name == name)
    }

    /// Get all component instances in this module (non-recursive)
    pub fn components(&self) -> Vec<&Component> {
        let mut components = Vec::new();
        for object in &self.objects {
            match object {
                CircuitObject::Component(component) => {
                    components.push(component);
                }
                CircuitObject::Series(series) => {
                    for member in &series.member {
                        components.push(member);
                    }
                }
                _ => {}
            }
        }
        components
    }

    /// Get all nested modules in this module (non-recursive)
    pub fn nested_modules(&self) -> Vec<&Module> {
        self.objects.iter()
            .filter_map(|obj| match obj {
                CircuitObject::Module(module) => Some(module),
                _ => None,
            })
            .collect()
    }
}

/// Convenience methods for Component
impl Component {
    /// Get a parameter value as a string
    pub fn get_param_str(&self, key: &str) -> Option<&str> {
        self.params.get(key)?.as_str()
    }

    /// Get a parameter value as a number
    pub fn get_param_number(&self, key: &str) -> Option<f64> {
        self.params.get(key)?.as_f64()
    }

    /// Check if component has a specific parameter
    pub fn has_param(&self, key: &str) -> bool {
        self.params.contains_key(key)
    }

    /// Check if component is marked as "NC" (not connected)
    pub fn is_not_connected(&self) -> bool {
        self.get_param_str("connection").map_or(false, |v| v == "NC")
    }
}
