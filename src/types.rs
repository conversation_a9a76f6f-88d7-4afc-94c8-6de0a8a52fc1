use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Root structure representing the entire circuit description
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitDescription {
    pub modules: Vec<Module>,
    #[serde(default)]
    pub components: Vec<ComponentDefinition>,
}

/// Represents a circuit module (hierarchical block)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Module {
    #[serde(rename = "type")]
    pub module_type: String,
    pub class: String,
    pub instance: String,
    #[serde(default)]
    pub objects: Vec<CircuitObject>,
    #[serde(default)]
    pub pins: ModulePins,
    #[serde(default)]
    pub nets: Vec<Net>,
    #[serde(default)]
    pub buses: Vec<Bus>,
}

/// Circuit objects that can be contained within modules
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum CircuitObject {
    #[serde(rename = "module")]
    Module(Module),
    #[serde(rename = "component")]
    Component(Component),
    #[serde(rename = "series")]
    Series(SeriesGroup),
}

/// Individual component instance
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Component {
    pub class: String,
    pub instance: String,
    #[serde(default)]
    pub params: HashMap<String, serde_json::Value>,
}

/// Series group of components
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SeriesGroup {
    pub instance: String,
    pub member: Vec<Component>,
}

/// Module pin definition
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ModulePin {
    Simple(String),
    Detailed {
        name: String,
        #[serde(rename = "type")]
        pin_type: PinType,
    },
}

/// Module pins can be either a simple string array or detailed pin objects
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ModulePins {
    Simple(Vec<String>),
    Detailed(Vec<ModulePin>),
}

impl Default for ModulePins {
    fn default() -> Self {
        ModulePins::Simple(Vec::new())
    }
}

impl ModulePins {
    /// Get the number of pins
    pub fn len(&self) -> usize {
        match self {
            ModulePins::Simple(pins) => pins.len(),
            ModulePins::Detailed(pins) => pins.len(),
        }
    }

    /// Check if pins collection is empty
    pub fn is_empty(&self) -> bool {
        self.len() == 0
    }
}

/// Pin direction/type
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum PinType {
    Input,
    Output,
    Inout,
}

/// Network connection definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Net {
    #[serde(default)]
    pub name: String,
    #[serde(rename = "type", default)]
    pub net_type: Option<String>,
    #[serde(default)]
    pub connection: Vec<String>,
    #[serde(default)]
    pub signals: Vec<BusSignal>,
}

/// Bus definition for grouped signals
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Bus {
    pub name: String,
    #[serde(default)]
    pub signals: Vec<String>,
    #[serde(default)]
    pub labels: Vec<String>,
}

/// Individual signal within a bus
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BusSignal {
    pub name: String,
    pub connection: Vec<String>,
}

/// Component definition/library entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentDefinition {
    #[serde(default)]
    pub name: String,
    #[serde(default)]
    pub class: String,
    #[serde(default)]
    pub partno: String,
    #[serde(default)]
    pub package: String,
    #[serde(default)]
    pub attr: HashMap<String, serde_json::Value>,
    #[serde(default)]
    pub pins: Vec<ComponentPin>,
    #[serde(default)]
    pub nets: Vec<Net>,
    #[serde(default)]
    pub buses: Vec<Bus>,
    #[serde(default)]
    pub objects: Vec<CircuitObject>,
}

/// Component pin definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentPin {
    pub id: serde_json::Value, // Can be number or string
    #[serde(default)]
    pub name: Vec<String>,
    #[serde(default)]
    pub attr: HashMap<String, serde_json::Value>,
}
