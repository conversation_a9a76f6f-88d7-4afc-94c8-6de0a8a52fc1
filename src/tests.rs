#[cfg(test)]
mod tests {
    use crate::*;

    /// Create a simple test circuit for testing
    fn create_test_circuit() -> CircuitDescription {
        let test_json = r#"
        {
            "modules": [
                {
                    "type": "module",
                    "class": "main",
                    "instance": "main",
                    "objects": [
                        {
                            "type": "component",
                            "class": "RES",
                            "instance": "R1",
                            "params": {
                                "rs": "10kΩ",
                                "pkg": "R0603"
                            }
                        },
                        {
                            "type": "module",
                            "class": "POWER",
                            "instance": "power",
                            "objects": [
                                {
                                    "type": "component",
                                    "class": "CAP",
                                    "instance": "C1",
                                    "params": {
                                        "cap": "100nF",
                                        "volt": "25V"
                                    }
                                }
                            ],
                            "pins": [
                                {"name": "VIN", "type": "input"},
                                {"name": "VOUT", "type": "output"}
                            ],
                            "nets": [
                                {
                                    "name": "VIN_NET",
                                    "connection": ["VIN", "C1.1"]
                                }
                            ],
                            "buses": []
                        }
                    ],
                    "pins": ["VDD", "GND"],
                    "nets": [
                        {
                            "name": "VDD_NET",
                            "connection": ["R1.1", "power.VIN"]
                        }
                    ],
                    "buses": []
                }
            ],
            "components": [
                {
                    "name": "RES",
                    "class": "",
                    "partno": "",
                    "package": "",
                    "pins": [
                        {"id": 1},
                        {"id": 2}
                    ]
                },
                {
                    "name": "CAP",
                    "class": "",
                    "partno": "",
                    "package": "",
                    "pins": [
                        {"id": 1},
                        {"id": 2}
                    ]
                }
            ]
        }
        "#;
        
        CircuitParser::parse_from_str(test_json).unwrap()
    }

    #[test]
    fn test_parse_reference_file() {
        // Note: The reference file contains JSON comments which are not valid JSON
        // This test demonstrates that the parser correctly identifies the JSON syntax error
        let result = CircuitParser::parse_from_file("refs/hbl.json");
        
        // We expect this to fail due to JSON comments in the file
        assert!(result.is_err(), "Reference file should fail to parse due to JSON comments");
        
        // Verify it's a JSON error
        match result.unwrap_err() {
            CircuitParseError::JsonError(_) => {
                // This is expected - the file contains JSON comments
            }
            other => panic!("Expected JsonError, got {:?}", other),
        }
    }

    #[test]
    fn test_parse_from_string() {
        let circuit = create_test_circuit();
        assert_eq!(circuit.modules.len(), 1);
        assert_eq!(circuit.components.len(), 2);
    }

    #[test]
    fn test_main_module_access() {
        let circuit = create_test_circuit();
        let main_module = circuit.main_module();
        assert!(main_module.is_some());
        
        let main = main_module.unwrap();
        assert_eq!(main.class, "main");
        assert_eq!(main.instance, "main");
    }

    #[test]
    fn test_find_module() {
        let circuit = create_test_circuit();
        
        // Find main module
        let main_module = circuit.find_module("main");
        assert!(main_module.is_some());
        
        // Find nested module
        let power_module = circuit.find_module("power");
        assert!(power_module.is_some());
        assert_eq!(power_module.unwrap().class, "POWER");
        
        // Try to find non-existent module
        let missing_module = circuit.find_module("nonexistent");
        assert!(missing_module.is_none());
    }

    #[test]
    fn test_find_component_definition() {
        let circuit = create_test_circuit();
        
        let res_def = circuit.find_component_definition("RES");
        assert!(res_def.is_some());
        assert_eq!(res_def.unwrap().pins.len(), 2);
        
        let cap_def = circuit.find_component_definition("CAP");
        assert!(cap_def.is_some());
        
        let missing_def = circuit.find_component_definition("MISSING");
        assert!(missing_def.is_none());
    }

    #[test]
    fn test_all_modules() {
        let circuit = create_test_circuit();
        let all_modules = circuit.all_modules();
        assert_eq!(all_modules.len(), 2); // main + power
    }

    #[test]
    fn test_all_components() {
        let circuit = create_test_circuit();
        let all_components = circuit.all_components();
        assert_eq!(all_components.len(), 2); // R1 + C1
    }

    #[test]
    fn test_module_methods() {
        let circuit = create_test_circuit();
        let main_module = circuit.main_module().unwrap();
        
        // Test find_component
        let r1 = main_module.find_component("R1");
        assert!(r1.is_some());
        assert_eq!(r1.unwrap().class, "RES");
        
        // Test components()
        let components = main_module.components();
        assert_eq!(components.len(), 1); // Only R1 in main module directly
        
        // Test nested_modules()
        let nested = main_module.nested_modules();
        assert_eq!(nested.len(), 1); // power module
        assert_eq!(nested[0].instance, "power");
    }

    #[test]
    fn test_component_parameters() {
        let circuit = create_test_circuit();
        let main_module = circuit.main_module().unwrap();
        let r1 = main_module.find_component("R1").unwrap();
        
        // Test parameter access
        assert_eq!(r1.get_param_str("rs"), Some("10kΩ"));
        assert_eq!(r1.get_param_str("pkg"), Some("R0603"));
        assert_eq!(r1.get_param_str("missing"), None);
        
        // Test has_param
        assert!(r1.has_param("rs"));
        assert!(!r1.has_param("missing"));
        
        // Test is_not_connected
        assert!(!r1.is_not_connected());
    }

    #[test]
    fn test_validation_errors() {
        // Test empty modules
        let empty_json = r#"{"modules": [], "components": []}"#;
        let result = CircuitParser::parse_from_str(empty_json);
        assert!(result.is_err());
        
        // Test missing required fields
        let invalid_json = r#"
        {
            "modules": [
                {
                    "type": "module",
                    "class": "",
                    "instance": "test"
                }
            ]
        }
        "#;
        let result = CircuitParser::parse_from_str(invalid_json);
        assert!(result.is_err());
    }

    #[test]
    fn test_malformed_json() {
        let malformed_json = r#"{"modules": [{"invalid": json}]}"#;
        let result = CircuitParser::parse_from_str(malformed_json);
        assert!(result.is_err());
        
        match result.unwrap_err() {
            CircuitParseError::JsonError(_) => {}, // Expected
            other => panic!("Expected JsonError, got {:?}", other),
        }
    }

    #[test]
    fn test_comprehensive_validation() {
        let circuit = create_test_circuit();
        let result = CircuitParser::validate_comprehensive(&circuit);
        assert!(result.is_ok(), "Comprehensive validation should pass: {:?}", result.err());
    }

    #[test]
    fn test_nc_component() {
        let nc_json = r#"
        {
            "modules": [
                {
                    "type": "module",
                    "class": "test",
                    "instance": "test",
                    "objects": [
                        {
                            "type": "component",
                            "class": "RES",
                            "instance": "R_NC",
                            "params": {
                                "rs": "10kΩ",
                                "connection": "NC"
                            }
                        }
                    ]
                }
            ]
        }
        "#;
        
        let circuit = CircuitParser::parse_from_str(nc_json).unwrap();
        let module = &circuit.modules[0];
        let component = module.find_component("R_NC").unwrap();
        
        assert!(component.is_not_connected());
    }
}
