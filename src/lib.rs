//! # Circuit Description Parser
//!
//! This crate provides a parser for circuit description files in JSON format.
//! It can parse hierarchical circuit designs with modules, components, connections, and parameters.
//!
//! ## Features
//!
//! - Parse JSON circuit description files
//! - Hierarchical module support
//! - Component parameter handling
//! - Connection validation
//! - Comprehensive error reporting
//! - Convenient API for accessing circuit data
//! - Automatic circuit layout generation
//! - Force-directed layout algorithm for optimal component placement
//! - Support for basic components (resistors, capacitors, etc.)
//! - Automatic chip layout with configurable pin arrangements
//!
//! ## Quick Start
//!
//! ```rust
//! use mclayout::{CircuitParser, LayoutEngine, Result};
//!
//! fn main() -> Result<()> {
//!     // Parse from file
//!     let circuit = CircuitParser::parse_from_file("circuit.json")?;
//!
//!     // Create layout engine
//!     let layout_engine = LayoutEngine::new()?;
//!
//!     // Generate layout for the entire circuit
//!     let layouts = layout_engine.layout_circuit(&circuit)?;
//!
//!     // Access layout results
//!     for layout in &layouts {
//!         println!("Module: {} at ({}, {})",
//!                  layout.module_id, layout.position.x, layout.position.y);
//!
//!         // Get all components in this module
//!         for component in layout.get_all_components() {
//!             println!("  Component: {} at ({}, {})",
//!                      component.component_id, component.position.x, component.position.y);
//!         }
//!     }
//!
//!     Ok(())
//! }
//! ```
//!
//! ## Data Structure Overview
//!
//! The parser represents circuits using the following main structures:
//!
//! - [`CircuitDescription`]: Root structure containing modules and component definitions
//! - [`Module`]: Hierarchical circuit blocks that can contain other modules and components
//! - [`Component`]: Individual circuit elements with parameters
//! - [`Net`]: Network connections between components
//! - [`Bus`]: Grouped signals for multi-wire connections
//!
//! ## Layout System
//!
//! The layout system provides automatic placement of circuit components:
//!
//! - [`LayoutEngine`]: Main layout engine with recursive bottom-up algorithm
//! - [`ComponentLayout`]: Layout result for individual components
//! - [`ModuleLayout`]: Layout result for modules containing multiple components
//! - [`LayoutConfig`]: Configuration parameters for layout algorithms
//!
//! ## Layout Configuration
//!
//! You can customize the layout behavior:
//!
//! ```rust
//! use mclayout::{LayoutEngine, LayoutConfig, Size};
//!
//! let mut config = LayoutConfig::default();
//! config.max_iterations = 500;
//! config.repulsion_strength = 150.0;
//! config.min_spacing = 10.0;
//!
//! let layout_engine = LayoutEngine::with_config(config)?;
//! ```
//!
//! ## Validation
//!
//! The parser includes comprehensive validation:
//!
//! ```rust
//! use mclayout::CircuitParser;
//!
//! let circuit = CircuitParser::parse_from_file("circuit.json")?;
//!
//! // Perform additional validation
//! CircuitParser::validate_comprehensive(&circuit)?;
//! ```

// Module declarations
pub mod error;
pub mod types;
pub mod parser;
pub mod utils;
pub mod layout;

// Test module
#[cfg(test)]
mod tests;

// Re-export main types and functions for convenience
pub use error::{CircuitParseError, Result};
pub use types::*;
pub use parser::CircuitParser;
pub use layout::*;


