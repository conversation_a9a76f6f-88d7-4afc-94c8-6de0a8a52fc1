//! # Circuit Description Parser
//!
//! This crate provides a parser for circuit description files in JSON format.
//! It can parse hierarchical circuit designs with modules, components, connections, and parameters.
//!
//! ## Features
//!
//! - Parse JSON circuit description files
//! - Hierarchical module support
//! - Component parameter handling
//! - Connection validation
//! - Comprehensive error reporting
//! - Convenient API for accessing circuit data
//!
//! ## Quick Start
//!
//! ```rust
//! use mclayout::{CircuitParser, Result};
//!
//! fn main() -> Result<()> {
//!     // Parse from file
//!     let circuit = CircuitParser::parse_from_file("circuit.json")?;
//!
//!     // Access the main module
//!     if let Some(main_module) = circuit.main_module() {
//!         println!("Main module: {}", main_module.instance);
//!
//!         // Find components
//!         for component in main_module.components() {
//!             println!("Component: {} ({})", component.instance, component.class);
//!         }
//!     }
//!
//!     // Find specific modules
//!     if let Some(power_module) = circuit.find_module("pwusb") {
//!         println!("Found power module: {}", power_module.class);
//!     }
//!
//!     Ok(())
//! }
//! ```
//!
//! ## Data Structure Overview
//!
//! The parser represents circuits using the following main structures:
//!
//! - [`CircuitDescription`]: Root structure containing modules and component definitions
//! - [`Module`]: Hierarchical circuit blocks that can contain other modules and components
//! - [`Component`]: Individual circuit elements with parameters
//! - [`Net`]: Network connections between components
//! - [`Bus`]: Grouped signals for multi-wire connections
//!
//! ## Validation
//!
//! The parser includes comprehensive validation:
//!
//! ```rust
//! use mclayout::CircuitParser;
//!
//! let circuit = CircuitParser::parse_from_file("circuit.json")?;
//!
//! // Perform additional validation
//! CircuitParser::validate_comprehensive(&circuit)?;
//! ```

// Module declarations
pub mod error;
pub mod types;
pub mod parser;
pub mod utils;

// Test module
#[cfg(test)]
mod tests;

// Re-export main types and functions for convenience
pub use error::{CircuitParseError, Result};
pub use types::*;
pub use parser::CircuitParser;


