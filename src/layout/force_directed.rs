use crate::layout::types::*;

/// Force-Directed布局引擎
pub struct ForceDirectedLayoutEngine {
    config: LayoutConfig,
}

impl ForceDirectedLayoutEngine {
    /// 创建新的力导向布局引擎
    pub fn new(config: LayoutConfig) -> Self {
        Self { config }
    }

    /// 对一组布局项进行力导向布局
    pub fn layout_items(&self, items: &mut [LayoutItem], nets: &[LayoutNet]) -> Result<(), Box<dyn std::error::Error>> {
        if items.len() <= 1 {
            return Ok(());
        }

        // 初始化位置（放在圆周上）
        self.initialize_positions(items);

        // 迭代优化
        for iteration in 0..self.config.max_iterations {
            let forces = self.calculate_forces(items, nets);
            let max_displacement = self.apply_forces(items, &forces);

            // 检查收敛
            if max_displacement < self.config.convergence_threshold {
                println!("Force-directed layout converged after {} iterations", iteration + 1);
                break;
            }
        }

        Ok(())
    }

    /// 初始化组件位置（圆形排列）
    fn initialize_positions(&self, items: &mut [LayoutItem]) {
        let count = items.len();
        let radius = (count as f64 * self.config.min_spacing) / (2.0 * std::f64::consts::PI);

        for (i, item) in items.iter_mut().enumerate() {
            let angle = 2.0 * std::f64::consts::PI * i as f64 / count as f64;
            let x = radius * angle.cos();
            let y = radius * angle.sin();

            match item {
                LayoutItem::Component(comp) => {
                    comp.position = Point::new(x, y);
                }
                LayoutItem::Module(module) => {
                    module.position = Point::new(x, y);
                }
            }
        }
    }

    /// 计算所有组件受到的力
    fn calculate_forces(&self, items: &[LayoutItem], nets: &[LayoutNet]) -> Vec<Force> {
        let mut forces = vec![Force::zero(); items.len()];

        // 计算斥力
        for i in 0..items.len() {
            for j in (i + 1)..items.len() {
                let repulsion = self.calculate_repulsion_force(
                    self.get_item_position(&items[i]),
                    self.get_item_position(&items[j]),
                    self.get_item_size(&items[i]),
                    self.get_item_size(&items[j]),
                );

                forces[i] = forces[i].add(&repulsion);
                forces[j] = forces[j].add(&Force::new(-repulsion.x, -repulsion.y));
            }
        }

        // 计算拉力
        for net in nets {
            let attraction_forces = self.calculate_attraction_forces(items, net);
            for (i, force) in attraction_forces.iter().enumerate() {
                if i < forces.len() {
                    forces[i] = forces[i].add(force);
                }
            }
        }

        forces
    }

    /// 计算两个组件之间的斥力
    fn calculate_repulsion_force(&self, pos1: Point, pos2: Point, size1: Size, size2: Size) -> Force {
        let distance_vec = pos1.subtract(&pos2);
        let distance = distance_vec.magnitude();

        if distance < 0.001 {
            // 避免除零，返回随机方向的力
            return Force::new(1.0, 0.0);
        }

        // 计算最小安全距离（考虑组件尺寸）
        let min_distance = (size1.width.max(size1.height) + size2.width.max(size2.height)) / 2.0 + self.config.min_spacing;

        let force_magnitude = if distance < min_distance {
            // 重叠或过近时，斥力急剧增大
            self.config.repulsion_strength * (min_distance - distance).powi(2) / distance.powi(2)
        } else if distance < min_distance * 3.0 {
            // 在一定范围内有斥力
            self.config.repulsion_strength / distance.powi(2)
        } else {
            // 距离足够远时斥力为零
            0.0
        };

        let direction = distance_vec.normalize();
        Force::new(direction.x * force_magnitude, direction.y * force_magnitude)
    }

    /// 计算网络连接产生的拉力
    fn calculate_attraction_forces(&self, items: &[LayoutItem], net: &LayoutNet) -> Vec<Force> {
        let mut forces = vec![Force::zero(); items.len()];

        if net.connected_pins.len() < 2 {
            return forces;
        }

        // 找到连接的组件
        let mut connected_items = Vec::new();
        for (component_id, _pin_id) in &net.connected_pins {
            for (i, item) in items.iter().enumerate() {
                if self.get_item_id(item) == component_id {
                    connected_items.push(i);
                    break;
                }
            }
        }

        if connected_items.len() < 2 {
            return forces;
        }

        // 计算重心
        let centroid = self.calculate_centroid(&connected_items, items);

        // 对每个连接的组件施加向重心的拉力
        for &item_index in &connected_items {
            let item_pos = self.get_item_position(&items[item_index]);
            let to_centroid = centroid.subtract(&item_pos);
            let distance = to_centroid.magnitude();

            if distance > 0.001 {
                let force_magnitude = self.config.attraction_strength * distance;
                let direction = to_centroid.normalize();
                forces[item_index] = forces[item_index].add(&Force::new(
                    direction.x * force_magnitude,
                    direction.y * force_magnitude,
                ));
            }
        }

        forces
    }

    /// 计算连接组件的重心
    fn calculate_centroid(&self, connected_items: &[usize], items: &[LayoutItem]) -> Point {
        if connected_items.is_empty() {
            return Point::origin();
        }

        let mut sum_x = 0.0;
        let mut sum_y = 0.0;

        for &index in connected_items {
            let pos = self.get_item_position(&items[index]);
            sum_x += pos.x;
            sum_y += pos.y;
        }

        Point::new(
            sum_x / connected_items.len() as f64,
            sum_y / connected_items.len() as f64,
        )
    }

    /// 应用力并移动组件
    fn apply_forces(&self, items: &mut [LayoutItem], forces: &[Force]) -> f64 {
        let mut max_displacement: f64 = 0.0;
        let damping = 0.9; // 阻尼系数
        let max_step = self.config.min_spacing; // 最大单步移动距离

        for (i, item) in items.iter_mut().enumerate() {
            if i < forces.len() {
                let force = &forces[i];
                let force_magnitude = force.magnitude();

                if force_magnitude > 0.001 {
                    // 限制最大移动距离
                    let step_size = (force_magnitude * damping).min(max_step);
                    let direction = Point::new(force.x / force_magnitude, force.y / force_magnitude);
                    let displacement = direction.scale(step_size);

                    match item {
                        LayoutItem::Component(comp) => {
                            comp.position = comp.position.add(&displacement);
                        }
                        LayoutItem::Module(module) => {
                            module.position = module.position.add(&displacement);
                        }
                    }

                    max_displacement = max_displacement.max(displacement.magnitude());
                }
            }
        }

        max_displacement
    }

    /// 获取布局项的位置
    fn get_item_position(&self, item: &LayoutItem) -> Point {
        match item {
            LayoutItem::Component(comp) => comp.position,
            LayoutItem::Module(module) => module.position,
        }
    }

    /// 获取布局项的尺寸
    fn get_item_size(&self, item: &LayoutItem) -> Size {
        match item {
            LayoutItem::Component(comp) => comp.size,
            LayoutItem::Module(module) => module.size,
        }
    }

    /// 获取布局项的ID
    fn get_item_id<'a>(&self, item: &'a LayoutItem) -> &'a str {
        match item {
            LayoutItem::Component(comp) => &comp.component_id,
            LayoutItem::Module(module) => &module.module_id,
        }
    }

    /// 计算布局的总体边界
    pub fn calculate_bounding_box(&self, items: &[LayoutItem]) -> (Point, Size) {
        if items.is_empty() {
            return (Point::origin(), Size::new(0.0, 0.0));
        }

        let mut min_x = f64::INFINITY;
        let mut max_x = f64::NEG_INFINITY;
        let mut min_y = f64::INFINITY;
        let mut max_y = f64::NEG_INFINITY;

        for item in items {
            let pos = self.get_item_position(item);
            let size = self.get_item_size(item);

            let left = pos.x - size.width / 2.0;
            let right = pos.x + size.width / 2.0;
            let top = pos.y - size.height / 2.0;
            let bottom = pos.y + size.height / 2.0;

            min_x = min_x.min(left);
            max_x = max_x.max(right);
            min_y = min_y.min(top);
            max_y = max_y.max(bottom);
        }

        let center = Point::new((min_x + max_x) / 2.0, (min_y + max_y) / 2.0);
        let size = Size::new(max_x - min_x, max_y - min_y);

        (center, size)
    }
}

impl Default for ForceDirectedLayoutEngine {
    fn default() -> Self {
        Self::new(LayoutConfig::default())
    }
}
