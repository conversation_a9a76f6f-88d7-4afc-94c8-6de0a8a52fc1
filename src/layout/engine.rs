use crate::layout::types::*;
use crate::layout::component_layout::ComponentLayoutEngine;
use crate::layout::chip_layout::ChipLayoutEngine;
use crate::layout::force_directed::ForceDirectedLayoutEngine;
use crate::types::{CircuitDescription, Module, CircuitObject, Component, Net};
use std::collections::HashMap;

/// 主布局引擎，实现自底向上的递归布局算法
pub struct LayoutEngine {
    config: LayoutConfig,
    component_engine: ComponentLayoutEngine,
    chip_engine: ChipLayoutEngine,
    force_engine: ForceDirectedLayoutEngine,
}

impl LayoutEngine {
    /// 创建新的布局引擎
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let config = LayoutConfig::default();
        Ok(Self {
            component_engine: ComponentLayoutEngine::new()?,
            chip_engine: ChipLayoutEngine::new(config.clone()),
            force_engine: ForceDirectedLayoutEngine::new(config.clone()),
            config,
        })
    }

    /// 使用自定义配置创建布局引擎
    pub fn with_config(config: LayoutConfig) -> Result<Self, Box<dyn std::error::Error>> {
        Ok(Self {
            component_engine: ComponentLayoutEngine::new()?,
            chip_engine: ChipLayoutEngine::new(config.clone()),
            force_engine: ForceDirectedLayoutEngine::new(config.clone()),
            config,
        })
    }

    /// 对整个电路描述进行布局
    pub fn layout_circuit(&self, circuit: &CircuitDescription) -> Result<Vec<ModuleLayout>, Box<dyn std::error::Error>> {
        let mut layouts = Vec::new();

        for module in &circuit.modules {
            let layout = self.layout_module(module)?;
            layouts.push(layout);
        }

        Ok(layouts)
    }

    /// 对单个模块进行递归布局
    pub fn layout_module(&self, module: &Module) -> Result<ModuleLayout, Box<dyn std::error::Error>> {
        // 1. 递归布局所有子对象
        let mut child_layouts = Vec::new();
        
        for object in &module.objects {
            match object {
                CircuitObject::Component(component) => {
                    let layout = self.layout_component(component)?;
                    child_layouts.push(LayoutItem::Component(layout));
                }
                CircuitObject::Module(sub_module) => {
                    let layout = self.layout_module(sub_module)?;
                    child_layouts.push(LayoutItem::Module(layout));
                }
                CircuitObject::Series(series) => {
                    // 对串联组件组进行布局
                    for component in &series.member {
                        let layout = self.layout_component(component)?;
                        child_layouts.push(LayoutItem::Component(layout));
                    }
                }
            }
        }

        // 2. 如果有多个子对象，使用力导向算法进行布局
        if child_layouts.len() > 1 {
            let layout_nets = self.convert_nets_to_layout_nets(&module.nets);
            self.force_engine.layout_items(&mut child_layouts, &layout_nets)?;
        }

        // 3. 计算模块的总体尺寸和边界
        let (center, size) = if child_layouts.is_empty() {
            (Point::origin(), Size::new(10.0, 10.0)) // 默认尺寸
        } else {
            self.force_engine.calculate_bounding_box(&child_layouts)
        };

        // 4. 调整子对象位置，使其相对于模块中心
        for child in &mut child_layouts {
            match child {
                LayoutItem::Component(comp) => {
                    comp.position = comp.position.subtract(&center);
                }
                LayoutItem::Module(module) => {
                    module.position = module.position.subtract(&center);
                }
            }
        }

        // 5. 生成模块的引脚布局
        let pins = self.generate_module_pins(module, size);

        Ok(ModuleLayout {
            module_id: module.instance.clone(),
            position: Point::origin(),
            rotation: Rotation::zero(),
            size,
            child_layouts,
            pins,
        })
    }

    /// 布局单个组件
    fn layout_component(&self, component: &Component) -> Result<ComponentLayout, Box<dyn std::error::Error>> {
        // 检查是否是芯片类型的组件
        if self.is_chip_component(component) {
            // 对于芯片，需要知道引脚数量
            let pin_count = self.estimate_pin_count(component);
            Ok(self.chip_engine.layout_chip(component, pin_count))
        } else {
            // 对于基础元器件，使用组件布局引擎
            self.component_engine.layout_component(component)
        }
    }

    /// 判断组件是否是芯片类型
    fn is_chip_component(&self, component: &Component) -> bool {
        // 简单的启发式判断：如果组件类名包含特定关键词，认为是芯片
        let chip_keywords = ["IC", "MCU", "CPU", "FPGA", "DSP", "ASIC", "SOC"];
        let class_upper = component.class.to_uppercase();
        
        chip_keywords.iter().any(|&keyword| class_upper.contains(keyword)) ||
        // 或者如果组件定义中没有找到对应的基础元器件定义
        self.component_engine.get_component_definition(&component.class).is_none()
    }

    /// 估算组件的引脚数量
    fn estimate_pin_count(&self, component: &Component) -> usize {
        // 从参数中尝试获取引脚数量
        if let Some(pin_count) = component.params.get("pins")
            .and_then(|v| v.as_u64())
            .map(|v| v as usize) {
            return pin_count;
        }

        // 根据组件类型估算默认引脚数量
        match component.class.to_uppercase().as_str() {
            s if s.contains("8051") => 40,
            s if s.contains("ARM") => 64,
            s if s.contains("FPGA") => 144,
            s if s.contains("MCU") => 32,
            s if s.contains("IC") => 14,
            _ => 8, // 默认8引脚
        }
    }

    /// 将网络定义转换为布局网络
    fn convert_nets_to_layout_nets(&self, nets: &[Net]) -> Vec<LayoutNet> {
        nets.iter().map(|net| {
            let connected_pins = net.connection.iter()
                .filter_map(|conn| {
                    // 解析连接字符串，格式通常是 "component.pin"
                    if let Some((component, pin)) = conn.split_once('.') {
                        Some((component.to_string(), pin.to_string()))
                    } else {
                        None
                    }
                })
                .collect();

            LayoutNet {
                net_id: net.name.clone(),
                connected_pins,
            }
        }).collect()
    }

    /// 生成模块的引脚布局
    fn generate_module_pins(&self, module: &Module, module_size: Size) -> Vec<PinLayout> {
        let pin_count = module.pins.len();
        if pin_count == 0 {
            return Vec::new();
        }

        // 使用芯片布局引擎生成引脚布局
        let dummy_component = Component {
            class: "MODULE".to_string(),
            instance: module.instance.clone(),
            params: HashMap::new(),
        };

        let chip_layout = self.chip_engine.layout_chip_from_module_pins(&dummy_component, &module.pins);
        
        // 调整引脚位置以适应实际模块尺寸
        chip_layout.pins.into_iter().map(|mut pin| {
            // 根据模块实际尺寸调整引脚位置
            let scale_x = module_size.width / chip_layout.size.width;
            let scale_y = module_size.height / chip_layout.size.height;
            
            pin.position.x *= scale_x;
            pin.position.y *= scale_y;
            
            pin
        }).collect()
    }

    /// 获取布局配置
    pub fn config(&self) -> &LayoutConfig {
        &self.config
    }

    /// 更新布局配置
    pub fn set_config(&mut self, config: LayoutConfig) {
        self.config = config.clone();
        self.chip_engine = ChipLayoutEngine::new(config.clone());
        self.force_engine = ForceDirectedLayoutEngine::new(config);
    }
}

impl Default for LayoutEngine {
    fn default() -> Self {
        Self::new().unwrap_or_else(|_| {
            let config = LayoutConfig::default();
            Self {
                component_engine: ComponentLayoutEngine::default(),
                chip_engine: ChipLayoutEngine::new(config.clone()),
                force_engine: ForceDirectedLayoutEngine::new(config.clone()),
                config,
            }
        })
    }
}

/// 布局结果的便捷方法
impl ModuleLayout {
    /// 获取所有组件布局
    pub fn get_all_components(&self) -> Vec<&ComponentLayout> {
        let mut components = Vec::new();
        self.collect_components(&mut components);
        components
    }

    /// 递归收集所有组件布局
    fn collect_components<'a>(&'a self, components: &mut Vec<&'a ComponentLayout>) {
        for child in &self.child_layouts {
            match child {
                LayoutItem::Component(comp) => {
                    components.push(comp);
                }
                LayoutItem::Module(module) => {
                    module.collect_components(components);
                }
            }
        }
    }

    /// 获取所有子模块布局
    pub fn get_all_modules(&self) -> Vec<&ModuleLayout> {
        let mut modules = Vec::new();
        self.collect_modules(&mut modules);
        modules
    }

    /// 递归收集所有子模块布局
    fn collect_modules<'a>(&'a self, modules: &mut Vec<&'a ModuleLayout>) {
        for child in &self.child_layouts {
            if let LayoutItem::Module(module) = child {
                modules.push(module);
                module.collect_modules(modules);
            }
        }
    }
}
