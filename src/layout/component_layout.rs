use crate::layout::types::*;
use crate::types::Component;
use std::collections::HashMap;
use std::fs;
use std::path::Path;

/// 基础元器件布局器
pub struct ComponentLayoutEngine {
    component_definitions: HashMap<String, LayoutComponentDefinition>,
}

impl ComponentLayoutEngine {
    /// 创建新的组件布局引擎
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let mut engine = Self {
            component_definitions: HashMap::new(),
        };
        engine.load_component_definitions()?;
        Ok(engine)
    }

    /// 从components目录加载组件定义
    fn load_component_definitions(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let components_dir = Path::new("components");
        if !components_dir.exists() {
            return Err("Components directory not found".into());
        }

        for entry in fs::read_dir(components_dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.extension().and_then(|s| s.to_str()) == Some("json") {
                let content = fs::read_to_string(&path)?;
                let component_def: LayoutComponentDefinition = serde_json::from_str(&content)?;
                self.component_definitions.insert(component_def.name.clone(), component_def);
            }
        }

        Ok(())
    }

    /// 为组件生成布局
    pub fn layout_component(&self, component: &Component) -> Result<ComponentLayout, Box<dyn std::error::Error>> {
        if let Some(definition) = self.component_definitions.get(&component.class) {
            self.create_layout_from_definition(component, definition)
        } else {
            // 如果没有找到定义，创建一个默认的布局
            self.create_default_layout(component)
        }
    }

    /// 根据组件定义创建布局
    fn create_layout_from_definition(
        &self,
        component: &Component,
        definition: &LayoutComponentDefinition,
    ) -> Result<ComponentLayout, Box<dyn std::error::Error>> {
        // 转换引脚信息
        let pins = definition.pins.iter().map(|pin_def| {
            PinLayout {
                pin_id: pin_def.number.clone(),
                position: Point::new(pin_def.position.x, pin_def.position.y),
                orientation: self.parse_pin_orientation(&pin_def.orientation),
                length: pin_def.length,
            }
        }).collect();

        // 转换绘图元素
        let drawing_elements = definition.elements.iter().map(|element| {
            DrawingElement {
                element_type: element.name.clone(),
                attributes: element.attrs.clone(),
            }
        }).collect();

        // 计算组件尺寸
        let size = self.calculate_component_size(definition);

        Ok(ComponentLayout {
            component_id: component.instance.clone(),
            position: Point::origin(),
            rotation: Rotation::zero(),
            size,
            pins,
            drawing_elements,
        })
    }

    /// 创建默认布局（当没有找到组件定义时）
    fn create_default_layout(&self, component: &Component) -> Result<ComponentLayout, Box<dyn std::error::Error>> {
        // 创建一个简单的矩形组件
        let size = Size::new(5.08, 5.08); // 默认0.2英寸正方形
        
        let mut drawing_elements = Vec::new();
        let mut attrs = HashMap::new();
        attrs.insert("x".to_string(), serde_json::json!(-size.width / 2.0));
        attrs.insert("y".to_string(), serde_json::json!(-size.height / 2.0));
        attrs.insert("width".to_string(), serde_json::json!(size.width));
        attrs.insert("height".to_string(), serde_json::json!(size.height));
        attrs.insert("stroke".to_string(), serde_json::Value::String("#000000".to_string()));
        attrs.insert("stroke-width".to_string(), serde_json::Value::String("0.254".to_string()));
        attrs.insert("fill".to_string(), serde_json::Value::String("none".to_string()));

        drawing_elements.push(DrawingElement {
            element_type: "rectangle".to_string(),
            attributes: attrs,
        });

        // 创建默认引脚（假设是两端器件）
        let pins = vec![
            PinLayout {
                pin_id: "1".to_string(),
                position: Point::new(0.0, size.height / 2.0 + 1.27),
                orientation: PinOrientation::Up,
                length: 1.27,
            },
            PinLayout {
                pin_id: "2".to_string(),
                position: Point::new(0.0, -size.height / 2.0 - 1.27),
                orientation: PinOrientation::Down,
                length: 1.27,
            },
        ];

        Ok(ComponentLayout {
            component_id: component.instance.clone(),
            position: Point::origin(),
            rotation: Rotation::zero(),
            size,
            pins,
            drawing_elements,
        })
    }

    /// 解析引脚方向
    pub fn parse_pin_orientation(&self, orientation: &str) -> PinOrientation {
        match orientation.to_lowercase().as_str() {
            "up" => PinOrientation::Up,
            "down" => PinOrientation::Down,
            "left" => PinOrientation::Left,
            "right" => PinOrientation::Right,
            _ => PinOrientation::Up, // 默认向上
        }
    }

    /// 计算组件尺寸
    fn calculate_component_size(&self, definition: &LayoutComponentDefinition) -> Size {
        let mut min_x = f64::INFINITY;
        let mut max_x = f64::NEG_INFINITY;
        let mut min_y = f64::INFINITY;
        let mut max_y = f64::NEG_INFINITY;

        // 从绘图元素计算边界
        for element in &definition.elements {
            if element.name == "rectangle" {
                if let (Some(x), Some(y), Some(width), Some(height)) = (
                    element.attrs.get("x").and_then(|v| v.as_f64()),
                    element.attrs.get("y").and_then(|v| v.as_f64()),
                    element.attrs.get("width").and_then(|v| v.as_f64()),
                    element.attrs.get("height").and_then(|v| v.as_f64()),
                ) {
                    min_x = min_x.min(x);
                    max_x = max_x.max(x + width);
                    min_y = min_y.min(y);
                    max_y = max_y.max(y + height);
                }
            } else if element.name == "polyline" {
                if let Some(points_str) = element.attrs.get("points").and_then(|v| v.as_str()) {
                    for point_pair in points_str.split_whitespace() {
                        if let Some((x_str, y_str)) = point_pair.split_once(',') {
                            if let (Ok(x), Ok(y)) = (x_str.parse::<f64>(), y_str.parse::<f64>()) {
                                min_x = min_x.min(x);
                                max_x = max_x.max(x);
                                min_y = min_y.min(y);
                                max_y = max_y.max(y);
                            }
                        }
                    }
                }
            }
        }

        // 如果没有找到有效的边界，使用默认尺寸
        if min_x == f64::INFINITY {
            Size::new(5.08, 5.08)
        } else {
            Size::new(max_x - min_x, max_y - min_y)
        }
    }

    /// 获取组件定义
    pub fn get_component_definition(&self, class: &str) -> Option<&LayoutComponentDefinition> {
        self.component_definitions.get(class)
    }

    /// 列出所有可用的组件类型
    pub fn list_component_types(&self) -> Vec<&str> {
        self.component_definitions.keys().map(|s| s.as_str()).collect()
    }
}

impl Default for ComponentLayoutEngine {
    fn default() -> Self {
        Self::new().unwrap_or_else(|_| Self {
            component_definitions: HashMap::new(),
        })
    }
}
