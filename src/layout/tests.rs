#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::*;
    use crate::layout::types::*;
    use crate::layout::component_layout::*;
    use crate::layout::chip_layout::*;
    use crate::layout::force_directed::*;
    use crate::layout::engine::*;
    use std::collections::HashMap;

    fn create_test_component(class: &str, instance: &str) -> Component {
        Component {
            class: class.to_string(),
            instance: instance.to_string(),
            params: HashMap::new(),
        }
    }

    fn create_test_module_with_components() -> Module {
        Module {
            module_type: "module".to_string(),
            class: "TestModule".to_string(),
            instance: "test".to_string(),
            objects: vec![
                CircuitObject::Component(create_test_component("R", "R1")),
                CircuitObject::Component(create_test_component("C", "C1")),
                CircuitObject::Component(create_test_component("IC", "U1")),
            ],
            pins: ModulePins::Simple(vec!["VCC".to_string(), "GND".to_string()]),
            nets: vec![
                Net {
                    name: "VCC".to_string(),
                    net_type: Some("power".to_string()),
                    connection: vec!["R1.1".to_string(), "U1.1".to_string()],
                    signals: vec![],
                },
            ],
            buses: vec![],
        }
    }

    #[test]
    fn test_layout_config_default() {
        let config = LayoutConfig::default();
        assert_eq!(config.max_iterations, 1000);
        assert_eq!(config.convergence_threshold, 0.1);
        assert_eq!(config.repulsion_strength, 100.0);
        assert_eq!(config.attraction_strength, 0.1);
        assert_eq!(config.min_spacing, 5.0);
        assert_eq!(config.chip_pin_spacing, 2.54);
    }

    #[test]
    fn test_point_operations() {
        let p1 = Point::new(1.0, 2.0);
        let p2 = Point::new(3.0, 4.0);
        
        let sum = p1.add(&p2);
        assert_eq!(sum.x, 4.0);
        assert_eq!(sum.y, 6.0);
        
        let diff = p2.subtract(&p1);
        assert_eq!(diff.x, 2.0);
        assert_eq!(diff.y, 2.0);
        
        let distance = p1.distance_to(&p2);
        assert!((distance - 2.828).abs() < 0.01);
    }

    #[test]
    fn test_force_operations() {
        let f1 = Force::new(1.0, 2.0);
        let f2 = Force::new(3.0, 4.0);
        
        let sum = f1.add(&f2);
        assert_eq!(sum.x, 4.0);
        assert_eq!(sum.y, 6.0);
        
        let magnitude = f1.magnitude();
        assert!((magnitude - 2.236).abs() < 0.01);
    }

    #[test]
    fn test_component_layout_engine_creation() {
        // 这个测试可能会失败如果components目录不存在，但不应该panic
        let result = ComponentLayoutEngine::new();
        // 我们只是确保它不会panic，结果可能是Ok或Err
        match result {
            Ok(_) => println!("ComponentLayoutEngine created successfully"),
            Err(e) => println!("ComponentLayoutEngine creation failed: {}", e),
        }
    }

    #[test]
    fn test_chip_layout_engine() {
        let config = LayoutConfig::default();
        let engine = ChipLayoutEngine::new(config);
        
        let component = create_test_component("IC", "U1");
        let layout = engine.layout_chip(&component, 14);
        
        assert_eq!(layout.component_id, "U1");
        assert_eq!(layout.pins.len(), 14);
        assert!(layout.size.width > 0.0);
        assert!(layout.size.height > 0.0);
    }

    #[test]
    fn test_force_directed_layout() {
        let config = LayoutConfig::default();
        let engine = ForceDirectedLayoutEngine::new(config);
        
        // 创建一些测试布局项
        let mut items = vec![
            LayoutItem::Component(ComponentLayout {
                component_id: "R1".to_string(),
                position: Point::new(0.0, 0.0),
                rotation: Rotation::zero(),
                size: Size::new(5.0, 2.0),
                pins: vec![],
                drawing_elements: vec![],
            }),
            LayoutItem::Component(ComponentLayout {
                component_id: "C1".to_string(),
                position: Point::new(1.0, 1.0),
                rotation: Rotation::zero(),
                size: Size::new(3.0, 3.0),
                pins: vec![],
                drawing_elements: vec![],
            }),
        ];
        
        let nets = vec![
            LayoutNet {
                net_id: "net1".to_string(),
                connected_pins: vec![
                    ("R1".to_string(), "1".to_string()),
                    ("C1".to_string(), "1".to_string()),
                ],
            },
        ];
        
        // 运行布局算法
        let result = engine.layout_items(&mut items, &nets);
        assert!(result.is_ok());
        
        // 验证组件位置已经改变
        if let LayoutItem::Component(comp) = &items[0] {
            // 位置应该已经从原点移动了
            assert!(comp.position.x != 0.0 || comp.position.y != 0.0);
        }
    }

    #[test]
    fn test_layout_engine_creation() {
        let result = LayoutEngine::new();
        match result {
            Ok(_) => println!("LayoutEngine created successfully"),
            Err(e) => println!("LayoutEngine creation failed: {}", e),
        }
    }

    #[test]
    fn test_module_layout() {
        if let Ok(engine) = LayoutEngine::new() {
            let module = create_test_module_with_components();
            let result = engine.layout_module(&module);
            
            match result {
                Ok(layout) => {
                    assert_eq!(layout.module_id, "test");
                    assert!(layout.size.width > 0.0);
                    assert!(layout.size.height > 0.0);
                    
                    // 应该有3个子组件
                    let components = layout.get_all_components();
                    assert_eq!(components.len(), 3);
                }
                Err(e) => println!("Module layout failed: {}", e),
            }
        }
    }

    #[test]
    fn test_circuit_layout() {
        if let Ok(engine) = LayoutEngine::new() {
            let circuit = CircuitDescription {
                modules: vec![create_test_module_with_components()],
                components: vec![],
            };
            
            let result = engine.layout_circuit(&circuit);
            match result {
                Ok(layouts) => {
                    assert_eq!(layouts.len(), 1);
                    assert_eq!(layouts[0].module_id, "test");
                }
                Err(e) => println!("Circuit layout failed: {}", e),
            }
        }
    }

    #[test]
    fn test_pin_orientation_parsing() {
        let engine = ComponentLayoutEngine::default();
        
        assert!(matches!(engine.parse_pin_orientation("up"), PinOrientation::Up));
        assert!(matches!(engine.parse_pin_orientation("down"), PinOrientation::Down));
        assert!(matches!(engine.parse_pin_orientation("left"), PinOrientation::Left));
        assert!(matches!(engine.parse_pin_orientation("right"), PinOrientation::Right));
        assert!(matches!(engine.parse_pin_orientation("invalid"), PinOrientation::Up));
    }

    #[test]
    fn test_chip_pin_distribution() {
        let config = LayoutConfig::default();
        let engine = ChipLayoutEngine::new(config);
        
        // 测试不同引脚数量的分布
        let test_cases = vec![
            (4, 4),   // 4引脚应该分布在4边
            (8, 8),   // 8引脚
            (14, 14), // 14引脚
            (20, 20), // 20引脚
        ];
        
        for (pin_count, expected_pins) in test_cases {
            let component = create_test_component("IC", &format!("U{}", pin_count));
            let layout = engine.layout_chip(&component, pin_count);
            assert_eq!(layout.pins.len(), expected_pins);
        }
    }

    #[test]
    fn test_bounding_box_calculation() {
        let config = LayoutConfig::default();
        let engine = ForceDirectedLayoutEngine::new(config);
        
        let items = vec![
            LayoutItem::Component(ComponentLayout {
                component_id: "R1".to_string(),
                position: Point::new(-5.0, -3.0),
                rotation: Rotation::zero(),
                size: Size::new(2.0, 1.0),
                pins: vec![],
                drawing_elements: vec![],
            }),
            LayoutItem::Component(ComponentLayout {
                component_id: "C1".to_string(),
                position: Point::new(3.0, 2.0),
                rotation: Rotation::zero(),
                size: Size::new(1.0, 2.0),
                pins: vec![],
                drawing_elements: vec![],
            }),
        ];
        
        let (center, size) = engine.calculate_bounding_box(&items);
        
        // 验证边界框计算
        assert!(center.x > -3.0 && center.x < 2.0);
        assert!(center.y > -1.0 && center.y < 1.0);
        assert!(size.width > 7.0);
        assert!(size.height > 4.0);
    }
}
