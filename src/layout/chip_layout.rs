use crate::layout::types::*;
use crate::types::{Component, ModulePins};

/// 芯片布局引擎
pub struct ChipLayoutEngine {
    config: LayoutConfig,
}

impl ChipLayoutEngine {
    /// 创建新的芯片布局引擎
    pub fn new(config: LayoutConfig) -> Self {
        Self { config }
    }

    /// 为芯片生成布局
    pub fn layout_chip(&self, component: &Component, pin_count: usize) -> ComponentLayout {
        let pins = self.generate_chip_pins(pin_count);
        let size = self.calculate_chip_size(pin_count);
        let drawing_elements = self.create_chip_drawing(size);

        ComponentLayout {
            component_id: component.instance.clone(),
            position: Point::origin(),
            rotation: Rotation::zero(),
            size,
            pins,
            drawing_elements,
        }
    }

    /// 从模块引脚信息生成芯片布局
    pub fn layout_chip_from_module_pins(&self, component: &Component, module_pins: &ModulePins) -> ComponentLayout {
        let pin_count = module_pins.len();
        let mut layout = self.layout_chip(component, pin_count);
        
        // 更新引脚名称
        match module_pins {
            ModulePins::Simple(pin_names) => {
                for (i, pin_layout) in layout.pins.iter_mut().enumerate() {
                    if i < pin_names.len() {
                        pin_layout.pin_id = pin_names[i].clone();
                    }
                }
            }
            ModulePins::Detailed(pins) => {
                for (i, pin_layout) in layout.pins.iter_mut().enumerate() {
                    if i < pins.len() {
                        match &pins[i] {
                            crate::types::ModulePin::Simple(name) => {
                                pin_layout.pin_id = name.clone();
                            }
                            crate::types::ModulePin::Detailed { name, .. } => {
                                pin_layout.pin_id = name.clone();
                            }
                        }
                    }
                }
            }
        }

        layout
    }

    /// 生成芯片引脚布局
    fn generate_chip_pins(&self, pin_count: usize) -> Vec<PinLayout> {
        if pin_count == 0 {
            return Vec::new();
        }

        let mut pins = Vec::new();
        let pin_spacing = self.config.chip_pin_spacing;
        let pin_length = self.config.chip_pin_length;

        // 计算每边的引脚数量
        let pins_per_side = self.calculate_pins_per_side(pin_count);
        
        let chip_size = self.calculate_chip_size(pin_count);
        let half_width = chip_size.width / 2.0;
        let half_height = chip_size.height / 2.0;

        let mut pin_number = 1;

        // 左边引脚
        if pins_per_side.left > 0 {
            let start_y = (pins_per_side.left as f64 - 1.0) * pin_spacing / 2.0;
            for i in 0..pins_per_side.left {
                pins.push(PinLayout {
                    pin_id: pin_number.to_string(),
                    position: Point::new(-half_width - pin_length, start_y - i as f64 * pin_spacing),
                    orientation: PinOrientation::Right,
                    length: pin_length,
                });
                pin_number += 1;
            }
        }

        // 下边引脚
        if pins_per_side.bottom > 0 {
            let start_x = -(pins_per_side.bottom as f64 - 1.0) * pin_spacing / 2.0;
            for i in 0..pins_per_side.bottom {
                pins.push(PinLayout {
                    pin_id: pin_number.to_string(),
                    position: Point::new(start_x + i as f64 * pin_spacing, -half_height - pin_length),
                    orientation: PinOrientation::Up,
                    length: pin_length,
                });
                pin_number += 1;
            }
        }

        // 右边引脚
        if pins_per_side.right > 0 {
            let start_y = -(pins_per_side.right as f64 - 1.0) * pin_spacing / 2.0;
            for i in 0..pins_per_side.right {
                pins.push(PinLayout {
                    pin_id: pin_number.to_string(),
                    position: Point::new(half_width + pin_length, start_y + i as f64 * pin_spacing),
                    orientation: PinOrientation::Left,
                    length: pin_length,
                });
                pin_number += 1;
            }
        }

        // 上边引脚
        if pins_per_side.top > 0 {
            let start_x = (pins_per_side.top as f64 - 1.0) * pin_spacing / 2.0;
            for i in 0..pins_per_side.top {
                pins.push(PinLayout {
                    pin_id: pin_number.to_string(),
                    position: Point::new(start_x - i as f64 * pin_spacing, half_height + pin_length),
                    orientation: PinOrientation::Down,
                    length: pin_length,
                });
                pin_number += 1;
            }
        }

        pins
    }

    /// 计算每边的引脚数量
    fn calculate_pins_per_side(&self, total_pins: usize) -> PinsPerSide {
        match total_pins {
            0 => PinsPerSide::new(0, 0, 0, 0),
            1..=4 => {
                // 少量引脚时，优先放在左右两边
                match total_pins {
                    1 => PinsPerSide::new(1, 0, 0, 0),
                    2 => PinsPerSide::new(1, 0, 1, 0),
                    3 => PinsPerSide::new(2, 0, 1, 0),
                    4 => PinsPerSide::new(2, 0, 2, 0),
                    _ => unreachable!(),
                }
            }
            _ => {
                // 多引脚时，均匀分布在四边
                let pins_per_side = (total_pins + 3) / 4; // 向上取整
                let mut left = pins_per_side;
                let mut bottom = pins_per_side;
                let mut right = pins_per_side;
                let mut top = total_pins - left - bottom - right;

                // 调整以确保总数正确
                while left + bottom + right + top > total_pins {
                    if top > 0 { top -= 1; }
                    else if right > 0 { right -= 1; }
                    else if bottom > 0 { bottom -= 1; }
                    else if left > 0 { left -= 1; }
                }

                PinsPerSide::new(left, bottom, right, top)
            }
        }
    }

    /// 计算芯片尺寸
    fn calculate_chip_size(&self, pin_count: usize) -> Size {
        let pins_per_side = self.calculate_pins_per_side(pin_count);
        let pin_spacing = self.config.chip_pin_spacing;
        let min_size = self.config.chip_min_size;

        // 根据引脚数量计算所需的最小尺寸
        let width_for_horizontal_pins = (pins_per_side.bottom.max(pins_per_side.top) as f64 * pin_spacing).max(min_size.width);
        let height_for_vertical_pins = (pins_per_side.left.max(pins_per_side.right) as f64 * pin_spacing).max(min_size.height);

        // 确保芯片尺寸足够容纳所有引脚，并且符合标准网格
        let width = self.round_to_grid(width_for_horizontal_pins + pin_spacing);
        let height = self.round_to_grid(height_for_vertical_pins + pin_spacing);

        Size::new(width, height)
    }

    /// 将尺寸舍入到标准网格
    fn round_to_grid(&self, value: f64) -> f64 {
        let grid = self.config.chip_pin_spacing;
        (value / grid).ceil() * grid
    }

    /// 创建芯片的绘图元素
    fn create_chip_drawing(&self, size: Size) -> Vec<DrawingElement> {
        let mut elements = Vec::new();
        let mut attrs = std::collections::HashMap::new();

        // 主体矩形
        attrs.insert("x".to_string(), serde_json::json!(-size.width / 2.0));
        attrs.insert("y".to_string(), serde_json::json!(-size.height / 2.0));
        attrs.insert("width".to_string(), serde_json::json!(size.width));
        attrs.insert("height".to_string(), serde_json::json!(size.height));
        attrs.insert("stroke".to_string(), serde_json::Value::String("#000000".to_string()));
        attrs.insert("stroke-width".to_string(), serde_json::Value::String("0.254".to_string()));
        attrs.insert("fill".to_string(), serde_json::Value::String("none".to_string()));

        elements.push(DrawingElement {
            element_type: "rectangle".to_string(),
            attributes: attrs,
        });

        // 添加引脚1标记（左上角的小圆点）
        let mut pin1_attrs = std::collections::HashMap::new();
        pin1_attrs.insert("cx".to_string(), serde_json::json!(-size.width / 2.0 + 1.0));
        pin1_attrs.insert("cy".to_string(), serde_json::json!(-size.height / 2.0 + 1.0));
        pin1_attrs.insert("r".to_string(), serde_json::json!(0.5));
        pin1_attrs.insert("fill".to_string(), serde_json::Value::String("#000000".to_string()));

        elements.push(DrawingElement {
            element_type: "circle".to_string(),
            attributes: pin1_attrs,
        });

        elements
    }
}

/// 每边的引脚数量
#[derive(Debug, Clone, Copy)]
struct PinsPerSide {
    left: usize,
    bottom: usize,
    right: usize,
    top: usize,
}

impl PinsPerSide {
    fn new(left: usize, bottom: usize, right: usize, top: usize) -> Self {
        Self { left, bottom, right, top }
    }
}

impl Default for ChipLayoutEngine {
    fn default() -> Self {
        Self::new(LayoutConfig::default())
    }
}
