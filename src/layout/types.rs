use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 2D坐标点
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Serialize, Deserialize)]
pub struct Point {
    pub x: f64,
    pub y: f64,
}

impl Point {
    pub fn new(x: f64, y: f64) -> Self {
        Self { x, y }
    }

    pub fn origin() -> Self {
        Self { x: 0.0, y: 0.0 }
    }

    /// 计算两点之间的距离
    pub fn distance_to(&self, other: &Point) -> f64 {
        ((self.x - other.x).powi(2) + (self.y - other.y).powi(2)).sqrt()
    }

    /// 向量加法
    pub fn add(&self, other: &Point) -> Point {
        Point {
            x: self.x + other.x,
            y: self.y + other.y,
        }
    }

    /// 向量减法
    pub fn subtract(&self, other: &Point) -> Point {
        Point {
            x: self.x - other.x,
            y: self.y - other.y,
        }
    }

    /// 向量缩放
    pub fn scale(&self, factor: f64) -> Point {
        Point {
            x: self.x * factor,
            y: self.y * factor,
        }
    }

    /// 向量长度
    pub fn magnitude(&self) -> f64 {
        (self.x.powi(2) + self.y.powi(2)).sqrt()
    }

    /// 单位向量
    pub fn normalize(&self) -> Point {
        let mag = self.magnitude();
        if mag > 0.0 {
            Point {
                x: self.x / mag,
                y: self.y / mag,
            }
        } else {
            Point::origin()
        }
    }
}

/// 尺寸定义
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Size {
    pub width: f64,
    pub height: f64,
}

impl Size {
    pub fn new(width: f64, height: f64) -> Self {
        Self { width, height }
    }

    pub fn area(&self) -> f64 {
        self.width * self.height
    }
}

/// 旋转角度（以度为单位）
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Rotation {
    pub degrees: f64,
}

impl Rotation {
    pub fn new(degrees: f64) -> Self {
        Self { degrees }
    }

    pub fn zero() -> Self {
        Self { degrees: 0.0 }
    }

    pub fn radians(&self) -> f64 {
        self.degrees.to_radians()
    }
}

/// 引脚布局信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PinLayout {
    pub pin_id: String,
    pub position: Point,
    pub orientation: PinOrientation,
    pub length: f64,
}

/// 引脚方向
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PinOrientation {
    Up,
    Down,
    Left,
    Right,
}

/// 组件布局结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentLayout {
    pub component_id: String,
    pub position: Point,
    pub rotation: Rotation,
    pub size: Size,
    pub pins: Vec<PinLayout>,
    pub drawing_elements: Vec<DrawingElement>,
}

/// 绘图元素
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DrawingElement {
    pub element_type: String,
    pub attributes: HashMap<String, serde_json::Value>,
}

/// 模块布局结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModuleLayout {
    pub module_id: String,
    pub position: Point,
    pub rotation: Rotation,
    pub size: Size,
    pub child_layouts: Vec<LayoutItem>,
    pub pins: Vec<PinLayout>,
}

/// 布局项（可以是组件或子模块）
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum LayoutItem {
    #[serde(rename = "component")]
    Component(ComponentLayout),
    #[serde(rename = "module")]
    Module(ModuleLayout),
}

/// 力导向布局的力向量
#[derive(Debug, Clone, Copy)]
pub struct Force {
    pub x: f64,
    pub y: f64,
}

impl Force {
    pub fn new(x: f64, y: f64) -> Self {
        Self { x, y }
    }

    pub fn zero() -> Self {
        Self { x: 0.0, y: 0.0 }
    }

    pub fn add(&self, other: &Force) -> Force {
        Force {
            x: self.x + other.x,
            y: self.y + other.y,
        }
    }

    pub fn magnitude(&self) -> f64 {
        (self.x.powi(2) + self.y.powi(2)).sqrt()
    }
}

/// 布局配置参数
#[derive(Debug, Clone)]
pub struct LayoutConfig {
    /// 力导向算法的最大迭代次数
    pub max_iterations: usize,
    /// 收敛阈值
    pub convergence_threshold: f64,
    /// 斥力系数
    pub repulsion_strength: f64,
    /// 拉力系数
    pub attraction_strength: f64,
    /// 最小组件间距
    pub min_spacing: f64,
    /// 芯片引脚间距
    pub chip_pin_spacing: f64,
    /// 芯片引脚长度
    pub chip_pin_length: f64,
    /// 芯片最小尺寸
    pub chip_min_size: Size,
}

impl Default for LayoutConfig {
    fn default() -> Self {
        Self {
            max_iterations: 1000,
            convergence_threshold: 0.1,
            repulsion_strength: 100.0,
            attraction_strength: 0.1,
            min_spacing: 5.0,
            chip_pin_spacing: 2.54, // 标准0.1英寸间距
            chip_pin_length: 2.54,
            chip_min_size: Size::new(5.08, 5.08), // 最小0.2英寸
        }
    }
}

/// 网络连接信息（用于力导向布局）
#[derive(Debug, Clone)]
pub struct LayoutNet {
    pub net_id: String,
    pub connected_pins: Vec<(String, String)>, // (component_id, pin_id)
}

/// 布局上下文，包含所有布局过程中需要的信息
#[derive(Debug, Clone)]
pub struct LayoutContext {
    pub config: LayoutConfig,
    pub component_definitions: HashMap<String, LayoutComponentDefinition>,
    pub nets: Vec<LayoutNet>,
}

/// 从components目录加载的组件定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayoutComponentDefinition {
    pub name: String,
    pub reference: String,
    pub elements: Vec<ComponentElement>,
    pub pins: Vec<ComponentPinDef>,
    pub properties: HashMap<String, serde_json::Value>,
}

/// 组件绘图元素
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentElement {
    pub name: String,
    pub attrs: HashMap<String, serde_json::Value>,
    pub unit: String,
}

/// 组件引脚定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentPinDef {
    pub number: String,
    pub name: String,
    #[serde(rename = "type")]
    pub pin_type: String,
    pub position: ComponentPinPosition,
    pub orientation: String,
    pub length: f64,
    pub unit: String,
}

/// 组件引脚位置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentPinPosition {
    pub x: f64,
    pub y: f64,
}
