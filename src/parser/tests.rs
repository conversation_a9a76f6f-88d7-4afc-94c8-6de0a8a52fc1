#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::*;
    use crate::parser::core::*;
    use crate::parser::validation::*;
    use crate::parser::utils::*;
    use std::collections::HashMap;

    fn create_test_circuit() -> CircuitDescription {
        CircuitDescription {
            modules: vec![
                Module {
                    module_type: "module".to_string(),
                    class: "TestModule".to_string(),
                    instance: "main".to_string(),
                    objects: vec![
                        CircuitObject::Component(Component {
                            class: "R".to_string(),
                            instance: "R1".to_string(),
                            params: {
                                let mut params = HashMap::new();
                                params.insert("value".to_string(), serde_json::Value::String("10k".to_string()));
                                params
                            },
                        }),
                        CircuitObject::Component(Component {
                            class: "C".to_string(),
                            instance: "C1".to_string(),
                            params: HashMap::new(),
                        }),
                    ],
                    pins: ModulePins::Simple(vec!["VCC".to_string(), "GND".to_string()]),
                    nets: vec![
                        Net {
                            name: "VCC".to_string(),
                            net_type: Some("power".to_string()),
                            connection: vec!["R1.1".to_string(), "VCC".to_string()],
                            signals: vec![],
                        },
                    ],
                    buses: vec![],
                }
            ],
            components: vec![
                ComponentDefinition {
                    name: "R".to_string(),
                    class: "Resistor".to_string(),
                    partno: "".to_string(),
                    package: "".to_string(),
                    attr: HashMap::new(),
                    pins: vec![
                        ComponentPin {
                            id: serde_json::Value::Number(1.into()),
                            name: vec!["~".to_string()],
                            attr: HashMap::new(),
                        },
                        ComponentPin {
                            id: serde_json::Value::Number(2.into()),
                            name: vec!["~".to_string()],
                            attr: HashMap::new(),
                        },
                    ],
                    nets: vec![],
                    buses: vec![],
                    objects: vec![],
                },
            ],
        }
    }

    #[test]
    fn test_circuit_parser_creation() {
        let parser = CircuitParser::new();
        assert_eq!(parser, CircuitParser::default());
    }

    #[test]
    fn test_json_parser() {
        let json_str = r#"{"modules": [], "components": []}"#;
        let result = JsonParser::parse_str(json_str);
        assert!(result.is_ok());
        
        let circuit = result.unwrap();
        assert!(circuit.modules.is_empty());
        assert!(circuit.components.is_empty());
    }

    #[test]
    fn test_json_syntax_validation() {
        let valid_json = r#"{"test": "value"}"#;
        assert!(JsonParser::validate_json_syntax(valid_json).is_ok());
        
        let invalid_json = r#"{"test": "value""#; // Missing closing brace
        assert!(JsonParser::validate_json_syntax(invalid_json).is_err());
    }

    #[test]
    fn test_circuit_parser_builder() {
        let builder = CircuitParserBuilder::new()
            .validate_on_parse(false)
            .allow_empty_modules(true);
        
        let json_str = r#"{"modules": [{"type": "module", "class": "Empty", "instance": "empty", "objects": [], "pins": [], "nets": [], "buses": []}], "components": []}"#;
        let result = builder.parse_from_str(json_str);
        assert!(result.is_ok());
    }

    #[test]
    fn test_circuit_validator_basic() {
        let circuit = create_test_circuit();
        assert!(CircuitValidator::validate_circuit(&circuit).is_ok());
    }

    #[test]
    fn test_circuit_validator_empty_modules() {
        let circuit = create_test_circuit();
        
        // Should fail with default settings
        let mut empty_circuit = circuit.clone();
        empty_circuit.modules[0].objects.clear();
        empty_circuit.modules[0].nets.clear();
        
        assert!(CircuitValidator::validate_circuit(&empty_circuit).is_err());
        
        // Should pass with allow_empty_modules = true
        assert!(CircuitValidator::validate_circuit_with_options(&empty_circuit, true).is_ok());
    }

    #[test]
    fn test_circuit_validator_missing_fields() {
        let mut circuit = create_test_circuit();
        
        // Test missing module class
        circuit.modules[0].class = "".to_string();
        assert!(CircuitValidator::validate_circuit(&circuit).is_err());
        
        // Reset and test missing instance
        circuit = create_test_circuit();
        circuit.modules[0].instance = "".to_string();
        assert!(CircuitValidator::validate_circuit(&circuit).is_err());
    }

    #[test]
    fn test_circuit_validator_component_validation() {
        let mut circuit = create_test_circuit();
        
        // Test component with spaces in instance name
        if let CircuitObject::Component(ref mut comp) = &mut circuit.modules[0].objects[0] {
            comp.instance = "R 1".to_string(); // Invalid: contains space
        }
        
        assert!(CircuitValidator::validate_circuit(&circuit).is_err());
    }

    #[test]
    fn test_parser_utils_extract_components() {
        let circuit = create_test_circuit();
        let components = ParserUtils::extract_all_components(&circuit);
        
        assert_eq!(components.len(), 2);
        assert_eq!(components[0].1.instance, "R1");
        assert_eq!(components[1].1.instance, "C1");
    }

    #[test]
    fn test_parser_utils_extract_modules() {
        let circuit = create_test_circuit();
        let modules = ParserUtils::extract_all_modules(&circuit);
        
        assert_eq!(modules.len(), 1);
        assert_eq!(modules[0].instance, "main");
    }

    #[test]
    fn test_parser_utils_find_module() {
        let circuit = create_test_circuit();
        
        let found = ParserUtils::find_module_by_name(&circuit, "main");
        assert!(found.is_some());
        assert_eq!(found.unwrap().instance, "main");
        
        let not_found = ParserUtils::find_module_by_name(&circuit, "nonexistent");
        assert!(not_found.is_none());
    }

    #[test]
    fn test_parser_utils_find_component_definition() {
        let circuit = create_test_circuit();
        
        let found = ParserUtils::find_component_definition(&circuit, "R");
        assert!(found.is_some());
        assert_eq!(found.unwrap().name, "R");
        
        let not_found = ParserUtils::find_component_definition(&circuit, "nonexistent");
        assert!(not_found.is_none());
    }

    #[test]
    fn test_parser_utils_main_module() {
        let circuit = create_test_circuit();
        let main_module = ParserUtils::get_main_module(&circuit);
        
        assert!(main_module.is_some());
        assert_eq!(main_module.unwrap().instance, "main");
    }

    #[test]
    fn test_parser_utils_counts() {
        let circuit = create_test_circuit();
        
        assert_eq!(ParserUtils::count_components(&circuit), 2);
        assert_eq!(ParserUtils::count_modules(&circuit), 1);
    }

    #[test]
    fn test_parser_utils_net_names() {
        let circuit = create_test_circuit();
        let net_names = ParserUtils::extract_net_names(&circuit);
        
        assert_eq!(net_names.len(), 1);
        assert_eq!(net_names[0], "VCC");
    }

    #[test]
    fn test_parser_utils_component_usage_stats() {
        let circuit = create_test_circuit();
        let stats = ParserUtils::create_component_usage_stats(&circuit);
        
        assert_eq!(stats.len(), 2);
        assert_eq!(stats.get("R"), Some(&1));
        assert_eq!(stats.get("C"), Some(&1));
    }

    #[test]
    fn test_parser_utils_find_components_by_class() {
        let circuit = create_test_circuit();
        let resistors = ParserUtils::find_components_by_class(&circuit, "R");
        
        assert_eq!(resistors.len(), 1);
        assert_eq!(resistors[0].1.instance, "R1");
    }

    #[test]
    fn test_parser_utils_find_components_with_parameter() {
        let circuit = create_test_circuit();
        let components_with_value = ParserUtils::find_components_with_parameter(&circuit, "value");
        
        assert_eq!(components_with_value.len(), 1);
        assert_eq!(components_with_value[0].1.instance, "R1");
    }

    #[test]
    fn test_circuit_statistics() {
        let circuit = create_test_circuit();
        let stats = ParserUtils::get_circuit_statistics(&circuit);
        
        assert_eq!(stats.total_modules, 1);
        assert_eq!(stats.total_components, 2);
        assert_eq!(stats.total_component_definitions, 1);
        assert_eq!(stats.total_nets, 1);
        assert_eq!(stats.total_buses, 0);
        assert_eq!(stats.unique_component_types, 2);
        assert!(!stats.has_hierarchy);
    }

    #[test]
    fn test_comprehensive_validation() {
        let circuit = create_test_circuit();
        assert!(CircuitValidator::validate_comprehensive(&circuit).is_ok());
    }

    #[test]
    fn test_parameter_validation() {
        let mut circuit = create_test_circuit();
        
        // Add component with invalid parameter (empty value)
        if let CircuitObject::Component(ref mut comp) = &mut circuit.modules[0].objects[0] {
            comp.params.insert("empty_param".to_string(), serde_json::Value::String("".to_string()));
        }
        
        assert!(CircuitValidator::validate_parameters(&circuit).is_err());
    }

    #[test]
    fn test_net_validation() {
        let mut circuit = create_test_circuit();
        
        // Add net with no connections
        circuit.modules[0].nets.push(Net {
            name: "empty_net".to_string(),
            net_type: None,
            connection: vec![],
            signals: vec![],
        });
        
        assert!(CircuitValidator::validate_circuit(&circuit).is_err());
    }
}
