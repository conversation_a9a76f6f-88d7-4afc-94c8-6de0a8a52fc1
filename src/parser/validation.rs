use crate::types::*;
use crate::error::*;
use std::collections::HashSet;

/// Circuit validation engine
pub struct CircuitValidator;

impl CircuitValidator {
    /// Validate the parsed circuit description
    pub fn validate_circuit(circuit: &CircuitDescription) -> Result<()> {
        Self::validate_circuit_with_options(circuit, false)
    }

    /// Validate circuit with configurable options
    pub fn validate_circuit_with_options(circuit: &CircuitDescription, allow_empty_modules: bool) -> Result<()> {
        // Validate that we have at least one module
        if circuit.modules.is_empty() {
            return Err(CircuitParseError::ValidationError(
                "Circuit must contain at least one module".to_string(),
            ));
        }

        // Validate each module
        for module in &circuit.modules {
            Self::validate_module(module, allow_empty_modules)?;
        }

        // Validate component definitions
        for component in &circuit.components {
            Self::validate_component_definition(component)?;
        }

        Ok(())
    }

    /// Validate a module
    pub fn validate_module(module: &Module, allow_empty: bool) -> Result<()> {
        if module.class.is_empty() {
            return Err(CircuitParseError::MissingField("module.class".to_string()));
        }
        if module.instance.is_empty() {
            return Err(CircuitParseError::MissingField("module.instance".to_string()));
        }

        // Check if module is empty when not allowed
        if !allow_empty && module.objects.is_empty() && module.nets.is_empty() && module.buses.is_empty() {
            return Err(CircuitParseError::ValidationError(
                format!("Module '{}' is empty (no objects, nets, or buses)", module.instance)
            ));
        }

        // Validate nested objects
        for object in &module.objects {
            match object {
                CircuitObject::Module(nested_module) => {
                    Self::validate_module(nested_module, allow_empty)?;
                }
                CircuitObject::Component(component) => {
                    Self::validate_component(component)?;
                }
                CircuitObject::Series(series) => {
                    Self::validate_series_group(series)?;
                }
            }
        }

        // Validate nets
        for net in &module.nets {
            Self::validate_net(net)?;
        }

        // Validate buses
        for bus in &module.buses {
            Self::validate_bus(bus)?;
        }

        Ok(())
    }

    /// Validate a component instance
    pub fn validate_component(component: &Component) -> Result<()> {
        if component.class.is_empty() {
            return Err(CircuitParseError::MissingField("component.class".to_string()));
        }
        if component.instance.is_empty() {
            return Err(CircuitParseError::MissingField("component.instance".to_string()));
        }

        // Validate component instance name format
        if component.instance.contains(' ') {
            return Err(CircuitParseError::ValidationError(
                format!("Component instance name '{}' contains spaces", component.instance)
            ));
        }

        Ok(())
    }

    /// Validate a series group
    pub fn validate_series_group(series: &SeriesGroup) -> Result<()> {
        if series.instance.is_empty() {
            return Err(CircuitParseError::MissingField("series.instance".to_string()));
        }
        if series.member.is_empty() {
            return Err(CircuitParseError::ValidationError(
                "Series group must contain at least one member".to_string(),
            ));
        }
        for member in &series.member {
            Self::validate_component(member)?;
        }
        Ok(())
    }

    /// Validate a net
    pub fn validate_net(net: &Net) -> Result<()> {
        // Net name can be empty for anonymous nets
        
        // Validate connections
        if net.connection.is_empty() && net.signals.is_empty() {
            return Err(CircuitParseError::ValidationError(
                format!("Net '{}' has no connections or signals", net.name)
            ));
        }

        // Validate bus signals
        for signal in &net.signals {
            Self::validate_bus_signal(signal)?;
        }

        Ok(())
    }

    /// Validate a bus
    pub fn validate_bus(bus: &Bus) -> Result<()> {
        if bus.name.is_empty() {
            return Err(CircuitParseError::MissingField("bus.name".to_string()));
        }

        // Bus should have either signals or labels
        if bus.signals.is_empty() && bus.labels.is_empty() {
            return Err(CircuitParseError::ValidationError(
                format!("Bus '{}' has no signals or labels", bus.name)
            ));
        }

        Ok(())
    }

    /// Validate a bus signal
    pub fn validate_bus_signal(signal: &BusSignal) -> Result<()> {
        if signal.name.is_empty() {
            return Err(CircuitParseError::MissingField("bus_signal.name".to_string()));
        }
        if signal.connection.is_empty() {
            return Err(CircuitParseError::ValidationError(
                format!("Bus signal '{}' has no connections", signal.name)
            ));
        }
        Ok(())
    }

    /// Validate a component definition
    pub fn validate_component_definition(component: &ComponentDefinition) -> Result<()> {
        // Component definitions can have either name or class, but should have some identifier
        if component.name.is_empty() && component.class.is_empty() {
            return Err(CircuitParseError::ValidationError(
                "Component definition must have either name or class".to_string(),
            ));
        }

        // Validate pins if present
        for pin in &component.pins {
            Self::validate_component_pin(pin)?;
        }

        Ok(())
    }

    /// Validate a component pin definition
    pub fn validate_component_pin(pin: &ComponentPin) -> Result<()> {
        // Pin ID should not be empty
        if pin.id.is_null() {
            return Err(CircuitParseError::ValidationError(
                "Component pin ID cannot be null".to_string(),
            ));
        }

        Ok(())
    }

    /// Validate net connections and references
    pub fn validate_connections(circuit: &CircuitDescription) -> Result<()> {
        for module in &circuit.modules {
            Self::validate_module_connections(module, &circuit.components)?;
        }
        Ok(())
    }

    /// Validate connections within a module
    pub fn validate_module_connections(module: &Module, component_defs: &[ComponentDefinition]) -> Result<()> {
        // Collect all available connection points
        let mut available_pins = HashSet::new();
        
        // Add module pins
        Self::collect_module_pins(&module.pins, &mut available_pins);

        // Add component pins from objects
        for object in &module.objects {
            match object {
                CircuitObject::Component(component) => {
                    Self::collect_component_pins(component, component_defs, &mut available_pins);
                }
                CircuitObject::Series(series) => {
                    // Add series connection points
                    available_pins.insert(format!("{}.1", series.instance));
                    available_pins.insert(format!("{}.2", series.instance));
                }
                CircuitObject::Module(nested_module) => {
                    // Recursively validate nested modules
                    Self::validate_module_connections(nested_module, component_defs)?;
                }
            }
        }

        // Validate net connections
        for net in &module.nets {
            for connection in &net.connection {
                if !connection.is_empty() && !available_pins.contains(connection) {
                    // Check if it's a bus signal reference
                    if !connection.contains('.') && !Self::is_valid_bus_reference(connection, module) {
                        return Err(CircuitParseError::ValidationError(
                            format!("Invalid connection reference: {} in module {}", connection, module.instance)
                        ));
                    }
                }
            }

            // Validate bus signals
            for signal in &net.signals {
                for connection in &signal.connection {
                    if !connection.is_empty() && !available_pins.contains(connection) {
                        return Err(CircuitParseError::ValidationError(
                            format!("Invalid bus signal connection: {} in net {}", connection, net.name)
                        ));
                    }
                }
            }
        }

        Ok(())
    }

    /// Collect module pins into the available pins set
    fn collect_module_pins(pins: &ModulePins, available_pins: &mut HashSet<String>) {
        match pins {
            ModulePins::Simple(pin_names) => {
                for name in pin_names {
                    available_pins.insert(name.clone());
                }
            }
            ModulePins::Detailed(pins) => {
                for pin in pins {
                    match pin {
                        ModulePin::Simple(name) => {
                            available_pins.insert(name.clone());
                        }
                        ModulePin::Detailed { name, .. } => {
                            available_pins.insert(name.clone());
                        }
                    }
                }
            }
        }
    }

    /// Collect component pins into the available pins set
    fn collect_component_pins(component: &Component, component_defs: &[ComponentDefinition], available_pins: &mut HashSet<String>) {
        // Find component definition to get pin information
        if let Some(comp_def) = component_defs.iter().find(|def| 
            def.name == component.class || def.class == component.class) {
            for pin in &comp_def.pins {
                let pin_ref = format!("{}.{}", component.instance, pin.id);
                available_pins.insert(pin_ref);
            }
        }
    }

    /// Check if a connection reference is a valid bus reference
    fn is_valid_bus_reference(connection: &str, module: &Module) -> bool {
        // Check if it's a module pin
        match &module.pins {
            ModulePins::Simple(pin_names) => {
                if pin_names.contains(&connection.to_string()) {
                    return true;
                }
            }
            ModulePins::Detailed(pins) => {
                for pin in pins {
                    match pin {
                        ModulePin::Simple(name) => {
                            if name == connection {
                                return true;
                            }
                        }
                        ModulePin::Detailed { name, .. } => {
                            if name == connection {
                                return true;
                            }
                        }
                    }
                }
            }
        }

        // Check if it's a bus name
        module.buses.iter().any(|bus| bus.name == connection)
    }

    /// Validate parameter values
    pub fn validate_parameters(circuit: &CircuitDescription) -> Result<()> {
        for module in &circuit.modules {
            Self::validate_module_parameters(module)?;
        }
        Ok(())
    }

    /// Validate parameters within a module
    fn validate_module_parameters(module: &Module) -> Result<()> {
        for object in &module.objects {
            match object {
                CircuitObject::Component(component) => {
                    Self::validate_component_parameters(component)?;
                }
                CircuitObject::Series(series) => {
                    for member in &series.member {
                        Self::validate_component_parameters(member)?;
                    }
                }
                CircuitObject::Module(nested_module) => {
                    Self::validate_module_parameters(nested_module)?;
                }
            }
        }
        Ok(())
    }

    /// Validate component parameters
    fn validate_component_parameters(component: &Component) -> Result<()> {
        // Check for common parameter issues
        for (key, value) in &component.params {
            // Check for malformed parameter values
            if let Some(str_val) = value.as_str() {
                if str_val.trim().is_empty() {
                    return Err(CircuitParseError::ValidationError(
                        format!("Empty parameter value for {} in component {}", key, component.instance)
                    ));
                }
                
                // Check for common typos in parameter names
                if key.contains(' ') && !key.starts_with("unknown-") {
                    return Err(CircuitParseError::ValidationError(
                        format!("Parameter name contains spaces: {} in component {}", key, component.instance)
                    ));
                }
            }
        }
        Ok(())
    }

    /// Perform comprehensive validation
    pub fn validate_comprehensive(circuit: &CircuitDescription) -> Result<()> {
        Self::validate_circuit(circuit)?;
        Self::validate_connections(circuit)?;
        Self::validate_parameters(circuit)?;
        Ok(())
    }
}
