use crate::types::*;
use crate::error::*;
use crate::layout::{LayoutEngine, ModuleLayout};
use crate::parser::validation::CircuitValidator;

/// Circuit parser for loading and parsing circuit description files
#[derive(Debug, PartialEq)]
pub struct CircuitParser;

impl CircuitParser {
    /// Create a new circuit parser
    pub fn new() -> Self {
        Self
    }

    /// Parse a circuit description from a JSON string
    pub fn parse_from_str(json_str: &str) -> Result<CircuitDescription> {
        let circuit: CircuitDescription = serde_json::from_str(json_str)?;
        CircuitValidator::validate_circuit(&circuit)?;
        Ok(circuit)
    }

    /// Parse a circuit description from a file
    pub fn parse_from_file<P: AsRef<std::path::Path>>(path: P) -> Result<CircuitDescription> {
        let content = std::fs::read_to_string(path)?;
        Self::parse_from_str(&content)
    }

    /// Parse a circuit description from a reader
    pub fn parse_from_reader<R: std::io::Read>(reader: R) -> Result<CircuitDescription> {
        let circuit: CircuitDescription = serde_json::from_reader(reader)?;
        CircuitValidator::validate_circuit(&circuit)?;
        Ok(circuit)
    }

    /// Parse and layout a circuit from file in one step
    pub fn parse_and_layout_from_file<P: AsRef<std::path::Path>>(path: P) -> Result<(CircuitDescription, Vec<ModuleLayout>)> {
        let circuit = Self::parse_from_file(path)?;
        let layout_engine = LayoutEngine::new()
            .map_err(|e| CircuitParseError::ValidationError(format!("Failed to create layout engine: {}", e)))?;
        let layouts = layout_engine.layout_circuit(&circuit)
            .map_err(|e| CircuitParseError::ValidationError(format!("Layout failed: {}", e)))?;
        Ok((circuit, layouts))
    }

    /// Parse and layout a circuit from string in one step
    pub fn parse_and_layout_from_str(json_str: &str) -> Result<(CircuitDescription, Vec<ModuleLayout>)> {
        let circuit = Self::parse_from_str(json_str)?;
        let layout_engine = LayoutEngine::new()
            .map_err(|e| CircuitParseError::ValidationError(format!("Failed to create layout engine: {}", e)))?;
        let layouts = layout_engine.layout_circuit(&circuit)
            .map_err(|e| CircuitParseError::ValidationError(format!("Layout failed: {}", e)))?;
        Ok((circuit, layouts))
    }

    /// Perform comprehensive validation
    pub fn validate_comprehensive(circuit: &CircuitDescription) -> Result<()> {
        CircuitValidator::validate_circuit(circuit)?;
        CircuitValidator::validate_connections(circuit)?;
        CircuitValidator::validate_parameters(circuit)?;
        Ok(())
    }
}

impl Default for CircuitParser {
    fn default() -> Self {
        Self::new()
    }
}

/// JSON format parser for circuit descriptions
pub struct JsonParser;

impl JsonParser {
    /// Parse circuit from JSON string with custom error handling
    pub fn parse_str(json_str: &str) -> Result<CircuitDescription> {
        serde_json::from_str(json_str).map_err(CircuitParseError::JsonError)
    }

    /// Parse circuit from JSON bytes
    pub fn parse_bytes(json_bytes: &[u8]) -> Result<CircuitDescription> {
        serde_json::from_slice(json_bytes).map_err(CircuitParseError::JsonError)
    }

    /// Parse circuit from reader
    pub fn parse_reader<R: std::io::Read>(reader: R) -> Result<CircuitDescription> {
        serde_json::from_reader(reader).map_err(CircuitParseError::JsonError)
    }

    /// Validate JSON syntax without full parsing
    pub fn validate_json_syntax(json_str: &str) -> Result<()> {
        let _: serde_json::Value = serde_json::from_str(json_str)?;
        Ok(())
    }
}

/// Builder pattern for configurable parsing
pub struct CircuitParserBuilder {
    validate_on_parse: bool,
    strict_mode: bool,
    allow_empty_modules: bool,
}

impl CircuitParserBuilder {
    /// Create a new parser builder
    pub fn new() -> Self {
        Self {
            validate_on_parse: true,
            strict_mode: false,
            allow_empty_modules: false,
        }
    }

    /// Enable or disable validation during parsing
    pub fn validate_on_parse(mut self, validate: bool) -> Self {
        self.validate_on_parse = validate;
        self
    }

    /// Enable strict mode (more rigorous validation)
    pub fn strict_mode(mut self, strict: bool) -> Self {
        self.strict_mode = strict;
        self
    }

    /// Allow empty modules
    pub fn allow_empty_modules(mut self, allow: bool) -> Self {
        self.allow_empty_modules = allow;
        self
    }

    /// Build the parser and parse from string
    pub fn parse_from_str(self, json_str: &str) -> Result<CircuitDescription> {
        let circuit: CircuitDescription = serde_json::from_str(json_str)?;
        
        if self.validate_on_parse {
            if self.strict_mode {
                CircuitValidator::validate_comprehensive(&circuit)?;
            } else {
                CircuitValidator::validate_circuit_with_options(&circuit, self.allow_empty_modules)?;
            }
        }
        
        Ok(circuit)
    }

    /// Build the parser and parse from file
    pub fn parse_from_file<P: AsRef<std::path::Path>>(self, path: P) -> Result<CircuitDescription> {
        let content = std::fs::read_to_string(path)?;
        self.parse_from_str(&content)
    }
}

impl Default for CircuitParserBuilder {
    fn default() -> Self {
        Self::new()
    }
}
