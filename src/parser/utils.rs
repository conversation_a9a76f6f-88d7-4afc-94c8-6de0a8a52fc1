use crate::types::*;
use std::collections::HashMap;

/// Parsing utilities and helper functions
pub struct ParserUtils;

impl ParserUtils {
    /// Extract all component instances from a circuit (flattened)
    pub fn extract_all_components(circuit: &CircuitDescription) -> Vec<(&Module, &Component)> {
        let mut components = Vec::new();
        for module in &circuit.modules {
            Self::collect_components_from_module(module, &mut components);
        }
        components
    }

    /// Recursively collect components from a module
    fn collect_components_from_module<'a>(module: &'a Module, components: &mut Vec<(&'a Module, &'a Component)>) {
        for object in &module.objects {
            match object {
                CircuitObject::Component(component) => {
                    components.push((module, component));
                }
                CircuitObject::Series(series) => {
                    for member in &series.member {
                        components.push((module, member));
                    }
                }
                CircuitObject::Module(nested_module) => {
                    Self::collect_components_from_module(nested_module, components);
                }
            }
        }
    }

    /// Extract all modules from a circuit (flattened)
    pub fn extract_all_modules(circuit: &CircuitDescription) -> Vec<&Module> {
        let mut modules = Vec::new();
        for module in &circuit.modules {
            Self::collect_modules_from_module(module, &mut modules);
        }
        modules
    }

    /// Recursively collect modules
    fn collect_modules_from_module<'a>(module: &'a Module, modules: &mut Vec<&'a Module>) {
        modules.push(module);
        for object in &module.objects {
            if let CircuitObject::Module(nested_module) = object {
                Self::collect_modules_from_module(nested_module, modules);
            }
        }
    }

    /// Find a module by instance name
    pub fn find_module_by_name<'a>(circuit: &'a CircuitDescription, name: &str) -> Option<&'a Module> {
        Self::find_module_recursive(&circuit.modules, name)
    }

    /// Recursively search for a module by name
    fn find_module_recursive<'a>(modules: &'a [Module], name: &str) -> Option<&'a Module> {
        for module in modules {
            if module.instance == name {
                return Some(module);
            }

            // Search in nested modules
            for object in &module.objects {
                if let CircuitObject::Module(nested_module) = object {
                    if let Some(found) = Self::find_module_recursive_single(nested_module, name) {
                        return Some(found);
                    }
                }
            }
        }
        None
    }

    /// Helper function to search in a single nested module
    fn find_module_recursive_single<'a>(module: &'a Module, name: &str) -> Option<&'a Module> {
        if module.instance == name {
            return Some(module);
        }

        for object in &module.objects {
            if let CircuitObject::Module(nested_module) = object {
                if let Some(found) = Self::find_module_recursive_single(nested_module, name) {
                    return Some(found);
                }
            }
        }
        None
    }

    /// Find a component definition by name or class
    pub fn find_component_definition<'a>(circuit: &'a CircuitDescription, name: &str) -> Option<&'a ComponentDefinition> {
        circuit.components.iter().find(|def| def.name == name || def.class == name)
    }

    /// Get the main/root module (first module in the list)
    pub fn get_main_module(circuit: &CircuitDescription) -> Option<&Module> {
        circuit.modules.first()
    }

    /// Count total number of components in the circuit
    pub fn count_components(circuit: &CircuitDescription) -> usize {
        Self::extract_all_components(circuit).len()
    }

    /// Count total number of modules in the circuit
    pub fn count_modules(circuit: &CircuitDescription) -> usize {
        Self::extract_all_modules(circuit).len()
    }

    /// Extract all net names from the circuit
    pub fn extract_net_names(circuit: &CircuitDescription) -> Vec<String> {
        let mut net_names = Vec::new();
        for module in &circuit.modules {
            Self::collect_net_names_from_module(module, &mut net_names);
        }
        net_names.sort();
        net_names.dedup();
        net_names
    }

    /// Recursively collect net names from a module
    fn collect_net_names_from_module(module: &Module, net_names: &mut Vec<String>) {
        for net in &module.nets {
            if !net.name.is_empty() {
                net_names.push(net.name.clone());
            }
        }

        for object in &module.objects {
            if let CircuitObject::Module(nested_module) = object {
                Self::collect_net_names_from_module(nested_module, net_names);
            }
        }
    }

    /// Extract all bus names from the circuit
    pub fn extract_bus_names(circuit: &CircuitDescription) -> Vec<String> {
        let mut bus_names = Vec::new();
        for module in &circuit.modules {
            Self::collect_bus_names_from_module(module, &mut bus_names);
        }
        bus_names.sort();
        bus_names.dedup();
        bus_names
    }

    /// Recursively collect bus names from a module
    fn collect_bus_names_from_module(module: &Module, bus_names: &mut Vec<String>) {
        for bus in &module.buses {
            bus_names.push(bus.name.clone());
        }

        for object in &module.objects {
            if let CircuitObject::Module(nested_module) = object {
                Self::collect_bus_names_from_module(nested_module, bus_names);
            }
        }
    }

    /// Create a component usage statistics map
    pub fn create_component_usage_stats(circuit: &CircuitDescription) -> HashMap<String, usize> {
        let mut stats = HashMap::new();
        let components = Self::extract_all_components(circuit);
        
        for (_, component) in components {
            *stats.entry(component.class.clone()).or_insert(0) += 1;
        }
        
        stats
    }

    /// Check if a circuit has any hierarchical modules
    pub fn has_hierarchical_modules(circuit: &CircuitDescription) -> bool {
        for module in &circuit.modules {
            if Self::module_has_nested_modules(module) {
                return true;
            }
        }
        false
    }

    /// Check if a module has nested modules
    fn module_has_nested_modules(module: &Module) -> bool {
        for object in &module.objects {
            if matches!(object, CircuitObject::Module(_)) {
                return true;
            }
        }
        false
    }

    /// Extract all parameter names used in the circuit
    pub fn extract_parameter_names(circuit: &CircuitDescription) -> Vec<String> {
        let mut param_names = Vec::new();
        let components = Self::extract_all_components(circuit);
        
        for (_, component) in components {
            for key in component.params.keys() {
                param_names.push(key.clone());
            }
        }
        
        param_names.sort();
        param_names.dedup();
        param_names
    }

    /// Find components by class name
    pub fn find_components_by_class<'a>(circuit: &'a CircuitDescription, class: &str) -> Vec<(&'a Module, &'a Component)> {
        Self::extract_all_components(circuit)
            .into_iter()
            .filter(|(_, component)| component.class == class)
            .collect()
    }

    /// Find components with specific parameter
    pub fn find_components_with_parameter<'a>(circuit: &'a CircuitDescription, param_name: &str) -> Vec<(&'a Module, &'a Component)> {
        Self::extract_all_components(circuit)
            .into_iter()
            .filter(|(_, component)| component.params.contains_key(param_name))
            .collect()
    }

    /// Get circuit statistics
    pub fn get_circuit_statistics(circuit: &CircuitDescription) -> CircuitStatistics {
        let all_components = Self::extract_all_components(circuit);
        let all_modules = Self::extract_all_modules(circuit);
        let component_usage = Self::create_component_usage_stats(circuit);
        let net_names = Self::extract_net_names(circuit);
        let bus_names = Self::extract_bus_names(circuit);
        let param_names = Self::extract_parameter_names(circuit);

        CircuitStatistics {
            total_modules: all_modules.len(),
            total_components: all_components.len(),
            total_component_definitions: circuit.components.len(),
            total_nets: net_names.len(),
            total_buses: bus_names.len(),
            unique_component_types: component_usage.len(),
            unique_parameter_names: param_names.len(),
            has_hierarchy: Self::has_hierarchical_modules(circuit),
            component_usage_stats: component_usage,
        }
    }
}

/// Circuit statistics structure
#[derive(Debug, Clone)]
pub struct CircuitStatistics {
    pub total_modules: usize,
    pub total_components: usize,
    pub total_component_definitions: usize,
    pub total_nets: usize,
    pub total_buses: usize,
    pub unique_component_types: usize,
    pub unique_parameter_names: usize,
    pub has_hierarchy: bool,
    pub component_usage_stats: HashMap<String, usize>,
}

impl CircuitStatistics {
    /// Print a formatted summary of the statistics
    pub fn print_summary(&self) {
        println!("Circuit Statistics:");
        println!("  Modules: {}", self.total_modules);
        println!("  Components: {}", self.total_components);
        println!("  Component Definitions: {}", self.total_component_definitions);
        println!("  Nets: {}", self.total_nets);
        println!("  Buses: {}", self.total_buses);
        println!("  Unique Component Types: {}", self.unique_component_types);
        println!("  Unique Parameter Names: {}", self.unique_parameter_names);
        println!("  Has Hierarchy: {}", self.has_hierarchy);
        
        if !self.component_usage_stats.is_empty() {
            println!("  Component Usage:");
            let mut sorted_usage: Vec<_> = self.component_usage_stats.iter().collect();
            sorted_usage.sort_by(|a, b| b.1.cmp(a.1));
            for (component_type, count) in sorted_usage {
                println!("    {}: {}", component_type, count);
            }
        }
    }
}
